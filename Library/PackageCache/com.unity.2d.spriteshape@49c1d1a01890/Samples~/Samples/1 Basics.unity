%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 9
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 3
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_IndirectSpecularColor: {r: 0, g: 0, b: 0, a: 1}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 11
  m_GIWorkflowMode: 1
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 0
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_FinalGather: 0
    m_FinalGatherFiltering: 1
    m_FinalGatherRayCount: 256
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 500
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 500
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 2
    m_PVRDenoiserTypeDirect: 0
    m_PVRDenoiserTypeIndirect: 0
    m_PVRDenoiserTypeAO: 0
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 0
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ShowResolutionOverlay: 1
    m_ExportTrainingData: 0
  m_LightingDataAsset: {fileID: 0}
  m_UseShadowmask: 1
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 2
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    accuratePlacement: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &1548241049
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1548241052}
  - component: {fileID: 1548241051}
  - component: {fileID: 1548241050}
  m_Layer: 0
  m_Name: Sprite Shape
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1548241050
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1548241049}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90539df1cd5704abcb25fec9f3f5f84b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Spline:
    m_IsOpenEnded: 0
    m_ControlPoints:
    - position: {x: -3.700837, y: -6.766616, z: 0}
      leftTangent: {x: 2.2290852, y: 0.0030808449, z: 0}
      rightTangent: {x: -2.2290854, y: -0.0030808449, z: 0}
      mode: 1
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 1
    - position: {x: -9.497262, y: -7.2778487, z: 0}
      leftTangent: {x: 2.4109612, y: -0.8270464, z: 0}
      rightTangent: {x: -2.4109612, y: 0.8270464, z: 0}
      mode: 1
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 1
    - position: {x: -11.833321, y: -0.3431406, z: 0}
      leftTangent: {x: 0.06476021, y: -1.367369, z: 0}
      rightTangent: {x: -0.06476021, y: 1.367369, z: 0}
      mode: 2
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 1
    - position: {x: -10.046913, y: 2.0612364, z: 0}
      leftTangent: {x: -1.1659508, y: -0.30263352, z: 0}
      rightTangent: {x: 1.1659508, y: 0.30263352, z: 0}
      mode: 1
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 1
    - position: {x: -5.4826956, y: 1.4389285, z: 0}
      leftTangent: {x: -3.6403365, y: -0.10659623, z: 0}
      rightTangent: {x: 3.6403365, y: 0.10659623, z: 0}
      mode: 1
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 1
    - position: {x: 0.14317775, y: 0.8184576, z: 0}
      leftTangent: {x: -2.1591895, y: -0.10868645, z: 0}
      rightTangent: {x: 2.1591895, y: 0.10868645, z: 0}
      mode: 1
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 1
    - position: {x: 4.145725, y: 1.6875689, z: 0}
      leftTangent: {x: -2.1027875, y: -0.09012818, z: 0}
      rightTangent: {x: 2.1027875, y: 0.09012818, z: 0}
      mode: 1
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 1
    - position: {x: 7.954253, y: 1.4916439, z: 0}
      leftTangent: {x: -1.3438807, y: -0.019803762, z: 0}
      rightTangent: {x: 2.5201635, y: 0.037137747, z: 0}
      mode: 1
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 1
    - position: {x: 11.347618, y: -1.087086, z: 0}
      leftTangent: {x: -0.14069843, y: 1.7271918, z: 0}
      rightTangent: {x: 0.14069843, y: -1.7271918, z: 0}
      mode: 2
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 1
    - position: {x: 9.503413, y: -6.5200095, z: 0}
      leftTangent: {x: 1.7579708, y: 0.8249035, z: 0}
      rightTangent: {x: -1.7579708, y: -0.824903, z: 0}
      mode: 1
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 1
    - position: {x: 3.4283261, y: -6.5464716, z: 0}
      leftTangent: {x: 3.0763397, y: 0.00007724762, z: 0}
      rightTangent: {x: -3.0763397, y: -0.00007724762, z: 0}
      mode: 1
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 1
  m_SpriteShape: {fileID: 11400000, guid: d39b7adf6769e40ecb09cfb263673d20, type: 2}
  m_FillPixelPerUnit: 50
  m_StretchTiling: 1
  m_SplineDetail: 16
  m_AdaptiveUV: 1
  m_StretchUV: 0
  m_WorldSpaceUV: 0
  m_ColliderDetail: 16
  m_ColliderOffset: 0
  m_UpdateCollider: 1
  m_OptimizeCollider: 1
  m_OptimizeGeometry: 1
--- !u!1971053207 &1548241051
SpriteShapeRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1548241049}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_MaskInteraction: 0
  m_ShapeTexture: {fileID: 2800000, guid: 9f145c794d2be4ed98c48eefdf471e02, type: 3}
  m_Sprites:
  - {fileID: 21300000, guid: 8a213466b4b3642faa34dd7853586d96, type: 3}
  - {fileID: 21300000, guid: 8c2b3b75a86844168b54c83f67a5fe8e, type: 3}
  m_LocalAABB:
    m_Center: {x: -0.22740936, y: -2.713738, z: -0.0050049997}
    m_Extent: {x: 11.608943, y: 4.830732, z: 0.0050049997}
--- !u!4 &1548241052
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1548241049}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1817488799
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1817488802}
  - component: {fileID: 1817488801}
  - component: {fileID: 1817488800}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!81 &1817488800
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1817488799}
  m_Enabled: 1
--- !u!20 &1817488801
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1817488799}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0, g: 0, b: 0, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_FocalLength: 50
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 1
  orthographic size: 10
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &1817488802
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1817488799}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}

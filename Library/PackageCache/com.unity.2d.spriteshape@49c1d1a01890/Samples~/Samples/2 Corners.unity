%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 9
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 3
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_IndirectSpecularColor: {r: 0, g: 0, b: 0, a: 1}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 11
  m_GIWorkflowMode: 1
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 0
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_FinalGather: 0
    m_FinalGatherFiltering: 1
    m_FinalGatherRayCount: 256
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 0
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 500
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 500
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 2
    m_PVRDenoiserTypeDirect: 0
    m_PVRDenoiserTypeIndirect: 0
    m_PVRDenoiserTypeAO: 0
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 0
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ShowResolutionOverlay: 1
    m_ExportTrainingData: 0
  m_LightingDataAsset: {fileID: 0}
  m_UseShadowmask: 1
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 2
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    accuratePlacement: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &148932347
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 148932351}
  - component: {fileID: 148932348}
  - component: {fileID: 148932349}
  m_Layer: 0
  m_Name: Background Castle Wall
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &148932348
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 148932347}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90539df1cd5704abcb25fec9f3f5f84b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Spline:
    m_IsOpenEnded: 0
    m_ControlPoints:
    - position: {x: -1.5087647, y: 4, z: 0}
      leftTangent: {x: 0, y: 0, z: 0}
      rightTangent: {x: 0, y: 0, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 1
    - position: {x: 1.5087647, y: 4, z: 0}
      leftTangent: {x: 0, y: 0, z: 0}
      rightTangent: {x: 0, y: 0, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 1
    - position: {x: 1.5087647, y: 8.5, z: 0}
      leftTangent: {x: 0, y: 0, z: 0}
      rightTangent: {x: 0, y: 0, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 1
    - position: {x: 8.549667, y: 8.5, z: 0}
      leftTangent: {x: 0, y: 0, z: 0}
      rightTangent: {x: 0, y: 0, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 1
    - position: {x: 8.549667, y: 4, z: 0}
      leftTangent: {x: 0, y: 0, z: 0}
      rightTangent: {x: 0, y: 0, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 1
    - position: {x: 11.567196, y: 4, z: 0}
      leftTangent: {x: 0, y: 0, z: 0}
      rightTangent: {x: 0, y: 0, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 1
    - position: {x: 11.567196, y: 8.5, z: 0}
      leftTangent: {x: 0, y: 0, z: 0}
      rightTangent: {x: 0, y: 0, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 1
    - position: {x: 19.613945, y: 8.5, z: 0}
      leftTangent: {x: 0, y: 0, z: 0}
      rightTangent: {x: 0, y: 0, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 1
    - position: {x: 19.613941, y: -8.5, z: 0}
      leftTangent: {x: 0.018205643, y: 1.2784569, z: 0}
      rightTangent: {x: -0.03827858, y: -2.6887, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 1
    - position: {x: -19.613949, y: -8.5, z: 0}
      leftTangent: {x: -0.009170532, y: -1.4367714, z: 0}
      rightTangent: {x: 0.009435654, y: 1.4787006, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 1
    - position: {x: -19.613947, y: 8.5, z: 0}
      leftTangent: {x: 0, y: 0, z: 0}
      rightTangent: {x: 0, y: 0, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 1
    - position: {x: -11.567201, y: 8.5, z: 0}
      leftTangent: {x: 0, y: 0, z: 0}
      rightTangent: {x: 0, y: 0, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 1
    - position: {x: -11.567199, y: 4, z: 0}
      leftTangent: {x: 0, y: 0, z: 0}
      rightTangent: {x: 0, y: 0, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 1
    - position: {x: -8.549672, y: 4, z: 0}
      leftTangent: {x: 0, y: 0, z: 0}
      rightTangent: {x: 0, y: 0, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 1
    - position: {x: -8.549667, y: 8.5, z: 0}
      leftTangent: {x: 0, y: 0, z: 0}
      rightTangent: {x: 0, y: 0, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 1
    - position: {x: -1.5087647, y: 8.5, z: 0}
      leftTangent: {x: 0, y: 0, z: 0}
      rightTangent: {x: 0, y: 0, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 1
  m_SpriteShape: {fileID: 11400000, guid: 49058e371087b49b18fb1c4177f3b7bd, type: 2}
  m_FillPixelPerUnit: 100
  m_StretchTiling: 1
  m_SplineDetail: 16
  m_AdaptiveUV: 1
  m_StretchUV: 0
  m_WorldSpaceUV: 0
  m_ColliderDetail: 4
  m_ColliderOffset: 0
  m_UpdateCollider: 0
  m_OptimizeCollider: 1
  m_OptimizeGeometry: 1
--- !u!1971053207 &148932349
SpriteShapeRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 148932347}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_MaskInteraction: 0
  m_ShapeTexture: {fileID: 2800000, guid: 815f15421880340e1ac8fb4767057d51, type: 3}
  m_Sprites:
  - {fileID: 21300006, guid: 2a23c00cde465461fb598526b91deedc, type: 3}
  - {fileID: 21300014, guid: 2a23c00cde465461fb598526b91deedc, type: 3}
  - {fileID: 21300002, guid: 2a23c00cde465461fb598526b91deedc, type: 3}
  - {fileID: 21300012, guid: a8321f6fae34f034787010a903e29648, type: 3}
  - {fileID: 21300010, guid: 2a23c00cde465461fb598526b91deedc, type: 3}
  - {fileID: 21300000, guid: 2a23c00cde465461fb598526b91deedc, type: 3}
  - {fileID: 21300004, guid: 2a23c00cde465461fb598526b91deedc, type: 3}
  - {fileID: 21300012, guid: 2a23c00cde465461fb598526b91deedc, type: 3}
  - {fileID: 21300008, guid: 2a23c00cde465461fb598526b91deedc, type: 3}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 21300012, guid: 2a23c00cde465461fb598526b91deedc, type: 3}
  - {fileID: 21300008, guid: 2a23c00cde465461fb598526b91deedc, type: 3}
  m_LocalAABB:
    m_Center: {x: -0.0000019073486, y: 0, z: -0.0050649997}
    m_Extent: {x: 20.113949, y: 9.000001, z: 0.0050649997}
--- !u!4 &148932351
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 148932347}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.9941908, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &743373521
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 743373524}
  - component: {fileID: 743373523}
  - component: {fileID: 743373522}
  m_Layer: 0
  m_Name: Platform Supports 02
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1971053207 &743373522
SpriteShapeRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 743373521}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 1
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_MaskInteraction: 0
  m_ShapeTexture: {fileID: 0}
  m_Sprites:
  - {fileID: 21300016, guid: 2a23c00cde465461fb598526b91deedc, type: 3}
  - {fileID: 0}
  m_LocalAABB:
    m_Center: {x: -0.24448252, y: 1.4645088, z: -0.00501}
    m_Extent: {x: 3.0169244, y: 1.4645088, z: 0.00501}
--- !u!114 &743373523
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 743373521}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90539df1cd5704abcb25fec9f3f5f84b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Spline:
    m_IsOpenEnded: 1
    m_ControlPoints:
    - position: {x: -3.244074, y: 1.6193502, z: 0}
      leftTangent: {x: 0, y: 0, z: 0}
      rightTangent: {x: 0, y: 0, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 0
    - position: {x: -1.657366, y: 1.6419101, z: 0}
      leftTangent: {x: -1.1476898, y: 0.70850587, z: 0}
      rightTangent: {x: 0.9156313, y: -0.56524897, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 1
      corner: 0
    - position: {x: 1.2286466, y: 1.6729, z: 0}
      leftTangent: {x: -0.48632717, y: 0.1658783, z: 0}
      rightTangent: {x: 1.9114523, y: -0.35046864, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 0
    - position: {x: 2.742056, y: 1.7106463, z: 0}
      leftTangent: {x: -2.1672134, y: 0.049874783, z: 0}
      rightTangent: {x: 2.55785, y: -0.058864117, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 1
      corner: 0
  m_SpriteShape: {fileID: 11400000, guid: e22936b0b355342d7b0855d7e938963e, type: 2}
  m_FillPixelPerUnit: 100
  m_StretchTiling: 1
  m_SplineDetail: 16
  m_AdaptiveUV: 1
  m_StretchUV: 0
  m_WorldSpaceUV: 0
  m_ColliderDetail: 4
  m_ColliderOffset: 0
  m_UpdateCollider: 0
  m_OptimizeCollider: 1
  m_OptimizeGeometry: 1
--- !u!4 &743373524
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 743373521}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.93, y: -3.03, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &863446550
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 863446553}
  - component: {fileID: 863446552}
  - component: {fileID: 863446551}
  m_Layer: 0
  m_Name: Platform 02
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1971053207 &863446551
SpriteShapeRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 863446550}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 2
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_MaskInteraction: 0
  m_ShapeTexture: {fileID: 0}
  m_Sprites:
  - {fileID: 21300018, guid: 2a23c00cde465461fb598526b91deedc, type: 3}
  - {fileID: 0}
  m_LocalAABB:
    m_Center: {x: 0.65924764, y: -0.34545112, z: -0.005}
    m_Extent: {x: 3.7870207, y: 1.0617188, z: 0.005}
--- !u!114 &863446552
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 863446550}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90539df1cd5704abcb25fec9f3f5f84b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Spline:
    m_IsOpenEnded: 1
    m_ControlPoints:
    - position: {x: -2.0958834, y: -0.39064693, z: 0}
      leftTangent: {x: 0, y: 0, z: 0}
      rightTangent: {x: 0, y: 0, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 0
    - position: {x: 3.4300003, y: -0.29999995, z: 0}
      leftTangent: {x: 0, y: 0, z: 0}
      rightTangent: {x: 0, y: 0, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 0
  m_SpriteShape: {fileID: 11400000, guid: d5a6d064bf23c452287ef7371144927c, type: 2}
  m_FillPixelPerUnit: 100
  m_StretchTiling: 1
  m_SplineDetail: 16
  m_AdaptiveUV: 1
  m_StretchUV: 0
  m_WorldSpaceUV: 0
  m_ColliderDetail: 4
  m_ColliderOffset: 0
  m_UpdateCollider: 0
  m_OptimizeCollider: 1
  m_OptimizeGeometry: 1
--- !u!4 &863446553
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 863446550}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.20000005, y: 0.16000013, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1108206408
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1108206412}
  - component: {fileID: 1108206409}
  - component: {fileID: 1108206410}
  m_Layer: 0
  m_Name: Platform 01
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1108206409
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1108206408}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90539df1cd5704abcb25fec9f3f5f84b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Spline:
    m_IsOpenEnded: 1
    m_ControlPoints:
    - position: {x: -7.25, y: -1.75, z: 0}
      leftTangent: {x: 1.4365501, y: -0.00849247, z: 0}
      rightTangent: {x: 1.4347038, y: 0.07331753, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 0
    - position: {x: -3.25, y: -2.25, z: 0}
      leftTangent: {x: -1.9918365, y: -0.24203491, z: 0}
      rightTangent: {x: 1.9918365, y: 0.24203491, z: 0}
      mode: 1
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 0
    - position: {x: 0.25, y: -0.75, z: 0}
      leftTangent: {x: -1.1856556, y: -0.51208687, z: 0}
      rightTangent: {x: 1.4588194, y: 0.6300669, z: 0}
      mode: 1
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 0
    - position: {x: 5.75, y: -0.75, z: 0}
      leftTangent: {x: -1.556633, y: 1.3201184, z: 0}
      rightTangent: {x: 1.7604151, y: -1.492938, z: 0}
      mode: 1
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 0
  m_SpriteShape: {fileID: 11400000, guid: d5a6d064bf23c452287ef7371144927c, type: 2}
  m_FillPixelPerUnit: 100
  m_StretchTiling: 1
  m_SplineDetail: 16
  m_AdaptiveUV: 1
  m_StretchUV: 0
  m_WorldSpaceUV: 0
  m_ColliderDetail: 16
  m_ColliderOffset: -1
  m_UpdateCollider: 0
  m_OptimizeCollider: 1
  m_OptimizeGeometry: 1
--- !u!1971053207 &1108206410
SpriteShapeRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1108206408}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 2
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_MaskInteraction: 0
  m_ShapeTexture: {fileID: 0}
  m_Sprites:
  - {fileID: 21300018, guid: 2a23c00cde465461fb598526b91deedc, type: 3}
  - {fileID: 0}
  m_LocalAABB:
    m_Center: {x: -0.6706662, y: -1.1484635, z: -0.005}
    m_Extent: {x: 7.8728127, y: 2.149938, z: 0.005}
--- !u!4 &1108206412
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1108206408}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -9.75, y: -3.25, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1242806276
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1242806280}
  - component: {fileID: 1242806279}
  - component: {fileID: 1242806278}
  - component: {fileID: 1242806277}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!81 &1242806277
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1242806276}
  m_Enabled: 1
--- !u!124 &1242806278
Behaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1242806276}
  m_Enabled: 1
--- !u!20 &1242806279
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1242806276}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0, g: 0, b: 0.19607843, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_FocalLength: 50
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 1
  orthographic size: 10
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &1242806280
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1242806276}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1680581995
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1680581999}
  - component: {fileID: 1680581996}
  - component: {fileID: 1680581997}
  m_Layer: 0
  m_Name: Platform 03
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1680581996
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1680581995}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90539df1cd5704abcb25fec9f3f5f84b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Spline:
    m_IsOpenEnded: 1
    m_ControlPoints:
    - position: {x: -6.5958834, y: -0.8906467, z: 0}
      leftTangent: {x: 0, y: 0, z: 0}
      rightTangent: {x: 0, y: 0, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 0
    - position: {x: -0.31037712, y: 0.21611452, z: 0}
      leftTangent: {x: -1.9731617, y: -0.3641789, z: 0}
      rightTangent: {x: 2.4578047, y: 0.4536276, z: 0}
      mode: 1
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 1
    - position: {x: 6.107708, y: 3.1037588, z: 0}
      leftTangent: {x: -2.9189305, y: -0.060035706, z: 0}
      rightTangent: {x: 1.9259801, y: 0.03961301, z: 0}
      mode: 1
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 0
  m_SpriteShape: {fileID: 11400000, guid: d5a6d064bf23c452287ef7371144927c, type: 2}
  m_FillPixelPerUnit: 100
  m_StretchTiling: 1
  m_SplineDetail: 16
  m_AdaptiveUV: 1
  m_StretchUV: 0
  m_WorldSpaceUV: 0
  m_ColliderDetail: 4
  m_ColliderOffset: 0
  m_UpdateCollider: 0
  m_OptimizeCollider: 1
  m_OptimizeGeometry: 1
--- !u!1971053207 &1680581997
SpriteShapeRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1680581995}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 2
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_MaskInteraction: 0
  m_ShapeTexture: {fileID: 0}
  m_Sprites:
  - {fileID: 21300018, guid: 2a23c00cde465461fb598526b91deedc, type: 3}
  - {fileID: 0}
  m_LocalAABB:
    m_Center: {x: -0.3205309, y: 1.064537, z: -0.005}
    m_Extent: {x: 7.4460506, y: 3.1131372, z: 0.005}
--- !u!4 &1680581999
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1680581995}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 11.83, y: -2.31, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1804489810
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1804489814}
  - component: {fileID: 1804489811}
  - component: {fileID: 1804489812}
  m_Layer: 0
  m_Name: Platform Supports 01
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1804489811
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1804489810}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90539df1cd5704abcb25fec9f3f5f84b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Spline:
    m_IsOpenEnded: 1
    m_ControlPoints:
    - position: {x: -7.06981, y: 1.6025028, z: 0}
      leftTangent: {x: 0, y: 0, z: 0}
      rightTangent: {x: 0, y: 0, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 0
    - position: {x: -5.6791887, y: 1.4878502, z: 0}
      leftTangent: {x: -1.1476898, y: 0.70850587, z: 0}
      rightTangent: {x: 0.9156313, y: -0.56524897, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 1
      corner: 0
    - position: {x: -2.8751068, y: 0.9929681, z: 0}
      leftTangent: {x: -0.48632717, y: 0.1658783, z: 0}
      rightTangent: {x: 1.9114523, y: -0.35046864, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 0
    - position: {x: -1.544878, y: 1.0378137, z: 0}
      leftTangent: {x: -2.1672134, y: 0.049874783, z: 0}
      rightTangent: {x: 2.55785, y: -0.058864117, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 1
      corner: 0
    - position: {x: 0.6537266, y: 2.6101074, z: 0}
      leftTangent: {x: -0.81578255, y: -1.1062441, z: 0}
      rightTangent: {x: 3.626093, y: 0.50750494, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 0
    - position: {x: 2.0591574, y: 2.694005, z: 0}
      leftTangent: {x: 0, y: 0, z: 0}
      rightTangent: {x: 0, y: 0, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 1
      corner: 0
    - position: {x: 4.5411544, y: 3.1485863, z: 0}
      leftTangent: {x: 0, y: 0, z: 0}
      rightTangent: {x: 0, y: 0, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 0
    - position: {x: 5.938326, y: 3.0802164, z: 0}
      leftTangent: {x: 0, y: 0, z: 0}
      rightTangent: {x: 0, y: 0, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 0
  m_SpriteShape: {fileID: 11400000, guid: e22936b0b355342d7b0855d7e938963e, type: 2}
  m_FillPixelPerUnit: 100
  m_StretchTiling: 1
  m_SplineDetail: 16
  m_AdaptiveUV: 1
  m_StretchUV: 0
  m_WorldSpaceUV: 0
  m_ColliderDetail: 4
  m_ColliderOffset: 0
  m_UpdateCollider: 0
  m_OptimizeCollider: 1
  m_OptimizeGeometry: 1
--- !u!1971053207 &1804489812
SpriteShapeRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1804489810}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 1
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_MaskInteraction: 0
  m_ShapeTexture: {fileID: 0}
  m_Sprites:
  - {fileID: 21300016, guid: 2a23c00cde465461fb598526b91deedc, type: 3}
  - {fileID: 0}
  m_LocalAABB:
    m_Center: {x: -0.5860243, y: 2.0703952, z: -0.00503}
    m_Extent: {x: 6.5839233, y: 2.295485, z: 0.00503}
--- !u!4 &1804489814
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1804489810}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -10.55, y: -7.64, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1897297955
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1897297958}
  - component: {fileID: 1897297957}
  - component: {fileID: 1897297956}
  m_Layer: 0
  m_Name: Platform Supports 03
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1971053207 &1897297956
SpriteShapeRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1897297955}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 1
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_MaskInteraction: 0
  m_ShapeTexture: {fileID: 0}
  m_Sprites:
  - {fileID: 21300016, guid: 2a23c00cde465461fb598526b91deedc, type: 3}
  - {fileID: 0}
  m_LocalAABB:
    m_Center: {x: -0.5359664, y: 2.7398272, z: -0.00503}
    m_Extent: {x: 6.960102, y: 3.3909473, z: 0.00503}
--- !u!114 &1897297957
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1897297955}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90539df1cd5704abcb25fec9f3f5f84b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Spline:
    m_IsOpenEnded: 1
    m_ControlPoints:
    - position: {x: -7.417392, y: 0.5650878, z: 0}
      leftTangent: {x: 0, y: 0, z: 0}
      rightTangent: {x: 0, y: 0, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 0
    - position: {x: -6.1325207, y: 0.6482034, z: 0}
      leftTangent: {x: -1.1476898, y: 0.70850587, z: 0}
      rightTangent: {x: 0.9156313, y: -0.56524897, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 1
      corner: 0
    - position: {x: -2.0394, y: 1.3667214, z: 0}
      leftTangent: {x: -0.48632717, y: 0.1658783, z: 0}
      rightTangent: {x: 1.9114523, y: -0.35046864, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 0
    - position: {x: -0.64398956, y: 1.420696, z: 0}
      leftTangent: {x: -2.1672134, y: 0.049874783, z: 0}
      rightTangent: {x: 2.55785, y: -0.058864117, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 1
      corner: 0
    - position: {x: 1.4602585, y: 2.7928703, z: 0}
      leftTangent: {x: -0.81578255, y: -1.1062441, z: 0}
      rightTangent: {x: 3.626093, y: 0.50750494, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 0
    - position: {x: 2.7366886, y: 2.8838847, z: 0}
      leftTangent: {x: 0, y: 0, z: 0}
      rightTangent: {x: 0, y: 0, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 1
      corner: 0
    - position: {x: 5.1822586, y: 4.9121447, z: 0}
      leftTangent: {x: 0, y: 0, z: 0}
      rightTangent: {x: 0, y: 0, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 0
    - position: {x: 6.4070053, y: 4.8949175, z: 0}
      leftTangent: {x: 0, y: 0, z: 0}
      rightTangent: {x: 0, y: 0, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 0
  m_SpriteShape: {fileID: 11400000, guid: e22936b0b355342d7b0855d7e938963e, type: 2}
  m_FillPixelPerUnit: 100
  m_StretchTiling: 1
  m_SplineDetail: 16
  m_AdaptiveUV: 1
  m_StretchUV: 0
  m_WorldSpaceUV: 0
  m_ColliderDetail: 4
  m_ColliderOffset: 0
  m_UpdateCollider: 0
  m_OptimizeCollider: 1
  m_OptimizeGeometry: 1
--- !u!4 &1897297958
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1897297955}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 12.15, y: -4.77, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 7
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}

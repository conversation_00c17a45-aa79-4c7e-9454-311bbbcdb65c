fileFormatVersion: 2
guid: 2a23c00cde465461fb598526b91deedc
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 21300000
    second: CWall_TopLeft
  - first:
      213: 21300002
    second: CWall_Top
  - first:
      213: 21300004
    second: CWall_TopRight
  - first:
      213: 21300006
    second: CWall_Right
  - first:
      213: 21300008
    second: CWall_BottomRight
  - first:
      213: 21300010
    second: CWall_Bottom
  - first:
      213: 21300012
    second: CWall_BottomLeft
  - first:
      213: 21300014
    second: CWall_Left
  - first:
      213: 21300016
    second: CastleWall_0
  - first:
      213: 21300018
    second: CastleBlock01
  - first:
      213: 21300020
    second: CastleWall_Stand
  externalObjects: {}
  serializedVersion: 10
  mipmaps:
    mipMapMode: 0
    enableMipMap: 1
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: -1
    mipBias: -100
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 0
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 64
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  platformSettings:
  - serializedVersion: 2
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
  - serializedVersion: 2
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
  - serializedVersion: 2
    buildTarget: iPhone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
  - serializedVersion: 2
    buildTarget: tvOS
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
  - serializedVersion: 2
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: CWall_TopLeft
      rect:
        serializedVersion: 2
        x: 0
        y: 447
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline:
      - - {x: -32, y: 32}
        - {x: -32, y: -32}
        - {x: 32, y: -32}
        - {x: 32, y: 32}
      physicsShape:
      - - {x: -32, y: 32}
        - {x: -32, y: -32}
        - {x: 32, y: -32}
        - {x: 32, y: 32}
      tessellationDetail: 0
      bones: []
      spriteID: 85bb4a12799104aea82ffd023631f5eb
      internalID: 21300000
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: CWall_Top
      rect:
        serializedVersion: 2
        x: 65
        y: 447
        width: 191
        height: 64
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline:
      - - {x: -95.5, y: 32}
        - {x: -95.5, y: -32}
        - {x: 95.5, y: -32}
        - {x: 95.5, y: 32}
      physicsShape:
      - - {x: -95.5, y: 32}
        - {x: -95.5, y: -32}
        - {x: 95.5, y: -32}
        - {x: 95.5, y: 32}
      tessellationDetail: 0
      bones: []
      spriteID: c0d4dbb40862b42719bed0178fc33863
      internalID: 21300002
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: CWall_TopRight
      rect:
        serializedVersion: 2
        x: 256
        y: 447
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline:
      - - {x: -32, y: 32}
        - {x: -32, y: -32}
        - {x: 32, y: -32}
        - {x: 32, y: 32}
      physicsShape:
      - - {x: -32, y: 32}
        - {x: -32, y: -32}
        - {x: 32, y: -32}
        - {x: 32, y: 32}
      tessellationDetail: 0
      bones: []
      spriteID: e691435be8e234c8ba6bbd44cf93bf9f
      internalID: 21300004
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: CWall_Right
      rect:
        serializedVersion: 2
        x: 193
        y: 383
        width: 192
        height: 64
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline:
      - - {x: -96, y: 32}
        - {x: -96, y: -32}
        - {x: 96, y: -32}
        - {x: 96, y: 32}
      physicsShape:
      - - {x: -96, y: 32}
        - {x: -96, y: -32}
        - {x: 96, y: -32}
        - {x: 96, y: 32}
      tessellationDetail: 0
      bones: []
      spriteID: e8d31f3d729f846cf901c9b695c0a286
      internalID: 21300006
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: CWall_BottomRight
      rect:
        serializedVersion: 2
        x: 385
        y: 447
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline:
      - - {x: -32, y: 32}
        - {x: -32, y: -32}
        - {x: 32, y: -32}
        - {x: 32, y: 32}
      physicsShape:
      - - {x: -32, y: 32}
        - {x: -32, y: -32}
        - {x: 32, y: -32}
        - {x: 32, y: 32}
      tessellationDetail: 0
      bones: []
      spriteID: b5f6cd57c80af465b9d7279b93edc0bd
      internalID: 21300008
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: CWall_Bottom
      rect:
        serializedVersion: 2
        x: 1
        y: 319
        width: 192
        height: 64
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline:
      - - {x: -96, y: 32}
        - {x: -96, y: -32}
        - {x: 96, y: -32}
        - {x: 96, y: 32}
      physicsShape:
      - - {x: -96, y: 32}
        - {x: -96, y: -32}
        - {x: 96, y: -32}
        - {x: 96, y: 32}
      tessellationDetail: 0
      bones: []
      spriteID: 940402297287e479a98329d757431010
      internalID: 21300010
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: CWall_BottomLeft
      rect:
        serializedVersion: 2
        x: 321
        y: 447
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline:
      - - {x: -32, y: 32}
        - {x: -32, y: -32}
        - {x: 32, y: -32}
        - {x: 32, y: 32}
      physicsShape:
      - - {x: -32, y: 32}
        - {x: -32, y: -32}
        - {x: 32, y: -32}
        - {x: 32, y: 32}
      tessellationDetail: 0
      bones: []
      spriteID: 5196786b599a246edb4ade9ef6e46b5f
      internalID: 21300012
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: CWall_Left
      rect:
        serializedVersion: 2
        x: 1
        y: 383
        width: 192
        height: 64
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline:
      - - {x: -96, y: 32}
        - {x: -96, y: -32}
        - {x: 96, y: -32}
        - {x: 96, y: 32}
      physicsShape:
      - - {x: -96, y: 32}
        - {x: -96, y: -32}
        - {x: 96, y: -32}
        - {x: 96, y: 32}
      tessellationDetail: 0
      bones: []
      spriteID: 6f193a5bf4ad94d5280067f257f134f8
      internalID: 21300014
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: CastleBlock01
      rect:
        serializedVersion: 2
        x: 0
        y: 127
        width: 257
        height: 128
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 65, y: 0, z: 64, w: 0}
      outline:
      - - {x: -66.5, y: 64}
        - {x: -128.5, y: 2}
        - {x: -128.5, y: -64}
        - {x: 67.5, y: -64}
        - {x: 128.5, y: -6}
        - {x: 128.5, y: 64}
      physicsShape:
      - - {x: -67.5, y: 64}
        - {x: -128.5, y: 2}
        - {x: -128.5, y: -64}
        - {x: 67.5, y: -64}
        - {x: 128.5, y: -2}
        - {x: 128.5, y: 64}
      tessellationDetail: 0
      bones: []
      spriteID: 9adb64494e8944defbfc84a91967bc6b
      internalID: 21300018
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: CastleWall_0
      rect:
        serializedVersion: 2
        x: 261
        y: 160
        width: 160
        height: 156
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline:
      - - {x: -21, y: 75}
        - {x: -69, y: 73}
        - {x: -71, y: 71}
        - {x: -74, y: 52}
        - {x: -73, y: 33}
        - {x: -80, y: 24}
        - {x: -80, y: -14}
        - {x: -70, y: -78}
        - {x: 36, y: -78}
        - {x: 59, y: -74}
        - {x: 80, y: -11}
        - {x: 80, y: 75}
      physicsShape:
      - - {x: 71.5, y: 63.5}
        - {x: -77.5, y: 35.5}
        - {x: -77.5, y: 9.5}
        - {x: -66.5, y: -63.5}
        - {x: 34.5, y: -63.5}
        - {x: 61.5, y: -33.5}
        - {x: 77.5, y: 47.5}
        - {x: 77.5, y: 63.5}
      tessellationDetail: 0
      bones: []
      spriteID: bbf07de53ae6044018a1edfbc9640bcb
      internalID: 21300016
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: e4f4d11a8c7904f3bb1c819a5022a54d
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
  spritePackingTag: 
  pSDRemoveMatte: 1
  pSDShowRemoveMatteOption: 1
  userData: 
  assetBundleName: 
  assetBundleVariant: 

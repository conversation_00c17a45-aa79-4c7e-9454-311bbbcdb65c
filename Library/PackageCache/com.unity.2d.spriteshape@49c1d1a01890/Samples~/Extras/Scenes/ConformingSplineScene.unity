%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 9
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 0
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_IndirectSpecularColor: {r: 0, g: 0, b: 0, a: 1}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 11
  m_GIWorkflowMode: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 1
    m_EnableRealtimeLightmaps: 1
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_FinalGather: 0
    m_FinalGatherFiltering: 1
    m_FinalGatherRayCount: 256
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 0
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 500
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 500
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 2
    m_PVRDenoiserTypeDirect: 0
    m_PVRDenoiserTypeIndirect: 0
    m_PVRDenoiserTypeAO: 0
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 0
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ShowResolutionOverlay: 1
    m_ExportTrainingData: 0
  m_LightingDataAsset: {fileID: 0}
  m_UseShadowmask: 1
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 2
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    accuratePlacement: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &400039572
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 400039576}
  - component: {fileID: 400039575}
  - component: {fileID: 400039574}
  m_Layer: 0
  m_Name: Top
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &400039574
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 400039572}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90539df1cd5704abcb25fec9f3f5f84b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_LegacyGenerator: 0
  m_Spline:
    m_IsOpenEnded: 1
    m_ControlPoints:
    - position: {x: 0.37061024, y: 7.517852, z: 0}
      leftTangent: {x: 0, y: 0, z: 0}
      rightTangent: {x: 0, y: 0, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 0
    - position: {x: 0.41290092, y: -1.8726089, z: 0}
      leftTangent: {x: -3.4678102, y: 1.8794639, z: 0}
      rightTangent: {x: 3.4678102, y: -1.8794639, z: 0}
      mode: 1
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 0
    - position: {x: 7.9664383, y: -6.63839, z: 0}
      leftTangent: {x: -4.8397465, y: -0.030138493, z: 0}
      rightTangent: {x: 4.8397465, y: 0.030138493, z: 0}
      mode: 1
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 0
    - position: {x: 15, y: -1, z: 0}
      leftTangent: {x: -3.2519875, y: -2.4569178, z: 0}
      rightTangent: {x: 3.2519875, y: 2.4569178, z: 0}
      mode: 1
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 0
    - position: {x: 14.186232, y: 7.786347, z: 0}
      leftTangent: {x: 0, y: 0, z: 0}
      rightTangent: {x: 0, y: 0, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 0
  m_SpriteShape: {fileID: 11400000, guid: 541c57e7ddb8adc46b7ca9573818a619, type: 2}
  m_FillPixelPerUnit: 100
  m_StretchTiling: 1
  m_SplineDetail: 16
  m_AdaptiveUV: 1
  m_StretchUV: 0
  m_WorldSpaceUV: 0
  m_ColliderDetail: 4
  m_ColliderOffset: 0
  m_UpdateCollider: 0
  m_OptimizeCollider: 1
--- !u!1971053207 &400039575
SpriteShapeRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 400039572}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_MaskInteraction: 0
  m_ShapeTexture: {fileID: 0}
  m_Sprites:
  - {fileID: 21300000, guid: e74b518a65bc45f4cace9a2fef6af29d, type: 3}
  m_LocalAABB:
    m_Center: {x: 7.558513, y: 0.5739784, z: -0.005}
    m_Extent: {x: 8.697825, y: 7.2123685, z: 0.005}
--- !u!4 &400039576
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 400039572}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -7.61, y: -0.47, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1865283555}
  m_Father: {fileID: 0}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1865283552
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1865283555}
  - component: {fileID: 1865283554}
  - component: {fileID: 1865283553}
  - component: {fileID: 1865283556}
  m_Layer: 0
  m_Name: Bottom
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1865283553
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1865283552}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 90539df1cd5704abcb25fec9f3f5f84b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_LegacyGenerator: 0
  m_Spline:
    m_IsOpenEnded: 1
    m_ControlPoints:
    - position: {x: 0.37061024, y: 7.517852, z: 0}
      leftTangent: {x: 0, y: 0, z: 0}
      rightTangent: {x: 0, y: 0, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 0
    - position: {x: 0.41290092, y: -1.8726089, z: 0}
      leftTangent: {x: -3.4678102, y: 1.8794639, z: 0}
      rightTangent: {x: 3.4678102, y: -1.8794639, z: 0}
      mode: 1
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 0
    - position: {x: 7.9664383, y: -6.63839, z: 0}
      leftTangent: {x: -4.8397465, y: -0.030138493, z: 0}
      rightTangent: {x: 4.8397465, y: 0.030138493, z: 0}
      mode: 1
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 0
    - position: {x: 15, y: -1, z: 0}
      leftTangent: {x: -3.2519875, y: -2.4569178, z: 0}
      rightTangent: {x: 3.2519875, y: 2.4569178, z: 0}
      mode: 1
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 0
    - position: {x: 14.186232, y: 7.786347, z: 0}
      leftTangent: {x: 0, y: 0, z: 0}
      rightTangent: {x: 0, y: 0, z: 0}
      mode: 0
      height: 1
      bevelCutoff: 0
      bevelSize: 0
      spriteIndex: 0
      corner: 0
  m_SpriteShape: {fileID: 11400000, guid: e03579ec9d1b6c54ea364bd8ee0bcfd8, type: 2}
  m_FillPixelPerUnit: 100
  m_StretchTiling: 1
  m_SplineDetail: 16
  m_AdaptiveUV: 1
  m_StretchUV: 0
  m_WorldSpaceUV: 0
  m_ColliderDetail: 4
  m_ColliderOffset: 0
  m_UpdateCollider: 0
  m_OptimizeCollider: 1
--- !u!1971053207 &1865283554
SpriteShapeRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1865283552}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_MaskInteraction: 0
  m_ShapeTexture: {fileID: 0}
  m_Sprites:
  - {fileID: 21300000, guid: 418ab5c27d3054eb89959d9c715e00c9, type: 3}
  - {fileID: 21300000, guid: e74b518a65bc45f4cace9a2fef6af29d, type: 3}
  m_LocalAABB:
    m_Center: {x: 7.5582957, y: 0.47995615, z: -0.005}
    m_Extent: {x: 9.09759, y: 7.5183325, z: 0.005}
--- !u!4 &1865283555
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1865283552}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 400039576}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1865283556
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1865283552}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: db6f6067a1e6dd34ca4e2c4b7145e79b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ParentObject: {fileID: 400039572}
--- !u!1 &1990748034
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1990748038}
  - component: {fileID: 1990748037}
  - component: {fileID: 1990748036}
  - component: {fileID: 1990748035}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!81 &1990748035
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1990748034}
  m_Enabled: 1
--- !u!124 &1990748036
Behaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1990748034}
  m_Enabled: 1
--- !u!20 &1990748037
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1990748034}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 2
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_FocalLength: 50
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 1
  orthographic size: 15
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &1990748038
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1990748034}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1, z: -10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}

{"name": "com.unity.2d.spriteshape", "version": "10.1.0", "unity": "6000.0", "displayName": "2D SpriteShape", "description": "SpriteShape Runtime & Editor Package contains the tooling and the runtime component that allows you to create very organic looking spline based 2D worlds. It comes with intuitive configurator and a highly performant renderer.", "keywords": ["2d", "shape", "spriteshape", "smartsprite", "spline", "terrain2d"], "category": "2D", "dependencies": {"com.unity.mathematics": "1.1.0", "com.unity.2d.common": "9.1.0", "com.unity.modules.physics2d": "1.0.0"}, "samples": [{"displayName": "Sprite <PERSON><PERSON><PERSON>", "description": "Samples to get started with SpriteShape", "path": "Samples~/Samples"}, {"displayName": "Sprite Shape Extras", "description": "This sample has utility scripts that has various use-cases of SpriteShape", "path": "Samples~/Extras"}], "relatedPackages": {"com.unity.2d.spriteshape.tests": "10.1.0", "com.unity.2d.common.tests": "9.1.0"}, "_upm": {"changelog": "### Changed\n- Update minimum Unity version."}, "upmCi": {"footprint": "0c356148e54789fed17954837eb293e091508863"}, "documentationUrl": "https://docs.unity3d.com/Packages/com.unity.2d.spriteshape@10.1/manual/index.html", "repository": {"url": "https://github.cds.internal.unity3d.com/unity/2d.git", "type": "git", "revision": "94a6127c9283f843ec1c9482222d58cde9b291e9"}, "_fingerprint": "49c1d1a01890c6450aa2753c572ad04566abe2b7"}
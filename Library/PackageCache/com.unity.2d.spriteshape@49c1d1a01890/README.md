# About 2D SpriteShape

Use the **2D SpriteShape** package as a powerful worldbuilding tool that allows you to tile Sprites along the path of a shape, with the Sprites automatically deforming in response to different angles according to their settings. For example, use **2D SpriteShapes** to quickly build various 2D platforms by editing their spline paths into different shapes.

# Installing 2D SpriteShape

To install this package, please follow the instructions in the [Package Manager documentation](https://docs.unity3d.com/Packages/com.unity.package-manager-ui@1.9/manual/index.html).

# Links to Feature Documentation and Useful Resources

* [2D SpriteShape Online Documentation](https://github.com/Unity-Technologies/2d-spriteshape-samples/blob/master/Documentation/SpriteShape.md)

* [2D SpriteShape Samples](https://github.com/Unity-Technologies/2d-spriteshape-samples)

* [2D SpriteShape Discussion Forums](https://forum.unity.com/threads/spriteshape-preview-package.522575/)

# Requirements

This version of **2D SpriteShape** is compatible with the following versions of the Unity Editor:

* 2018.1 and later (recommended)
# About Tilemap Editor

Use Unity’s Tilemap Editor to create and edit a variety of 2D levels using Tile Assets arranged on the Grid and Tilemap GameObjects. This also supports specialized types of Tilemaps, such as Hexagonal and Isometric Tilemaps.

# Installing Tilemap Editor

To install this package, follow the instructions in the [Package Manager documentation](https://docs.unity3d.com/Packages/com.unity.package-manager-ui@latest/index.html).

# Using Tilemap Editor

The Tilemap Manual can be found [here] (http://docs.unity3d.com/Documentation/Manual/class-Tilemap.html).

The Tile Palette Manual can be found [here] (https://docs.unity3d.com/Manual/Tilemap-Palette.html).

# Technical details
## Requirements

This version of Tilemap Editor is compatible with the following versions of the Unity Editor:

* 2019.2 and later (recommended)

## Package contents

The following table indicates the folder structure of the Tilemap Editor package:

|Location|Description|
|---|---|
|`<Editor>`|Root folder containing the source for the Tilemap Editor used to edit Tilemaps inside the Unity Editor.|
|`<Tests>`|Root folder containing the source for the tests for Tilemap Editor used the Unity Editor Test Runner.|

## Document revision history

|Date|Reason|
|---|---|
|January 2, 2019|Document created. Matches package version 1.0.0|

<?xml version="1.0" encoding="utf-8"?>
<test-run id="2" testcasecount="24" result="Failed(Child)" total="24" passed="18" failed="6" inconclusive="0" skipped="0" asserts="0" engine-version="*******" clr-version="4.0.30319.42000" start-time="2025-08-05 06:29:39Z" end-time="2025-08-05 06:29:39Z" duration="0.2838939">
  <test-suite type="TestSuite" id="1317" name="2dMobile2" fullname="2dMobile2" runstate="Runnable" testcasecount="289" result="Failed" site="Child" start-time="2025-08-05 06:29:39Z" end-time="2025-08-05 06:29:39Z" duration="0.283894" total="24" passed="18" failed="6" inconclusive="0" skipped="0" asserts="0">
    <properties>
      <property name="platform" value="EditMode" />
    </properties>
    <failure>
      <message><![CDATA[One or more child tests had errors]]></message>
    </failure>
    <test-suite type="Assembly" id="1310" name="MobileScrollingGame.Tests.dll" fullname="/Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/ScriptAssemblies/MobileScrollingGame.Tests.dll" runstate="Runnable" testcasecount="289" result="Failed" site="Child" start-time="2025-08-05 06:29:39Z" end-time="2025-08-05 06:29:39Z" duration="0.268320" total="24" passed="18" failed="6" inconclusive="0" skipped="0" asserts="0">
      <properties>
        <property name="_PID" value="4631" />
        <property name="_APPDOMAIN" value="Unity Child Domain" />
        <property name="platform" value="EditMode" />
        <property name="EditorOnly" value="True" />
      </properties>
      <failure>
        <message><![CDATA[One or more child tests had errors]]></message>
      </failure>
      <test-suite type="TestSuite" id="1311" name="MobileScrollingGame" fullname="MobileScrollingGame" runstate="Runnable" testcasecount="289" result="Failed" site="Child" start-time="2025-08-05 06:29:39Z" end-time="2025-08-05 06:29:39Z" duration="0.267607" total="24" passed="18" failed="6" inconclusive="0" skipped="0" asserts="0">
        <properties />
        <failure>
          <message><![CDATA[One or more child tests had errors]]></message>
        </failure>
        <test-suite type="TestSuite" id="1312" name="Tests" fullname="MobileScrollingGame.Tests" runstate="Runnable" testcasecount="289" result="Failed" site="Child" start-time="2025-08-05 06:29:39Z" end-time="2025-08-05 06:29:39Z" duration="0.267130" total="24" passed="18" failed="6" inconclusive="0" skipped="0" asserts="0">
          <properties />
          <failure>
            <message><![CDATA[One or more child tests had errors]]></message>
          </failure>
          <test-suite type="TestFixture" id="1020" name="CameraFollowerTests" fullname="MobileScrollingGame.Tests.CameraFollowerTests" classname="MobileScrollingGame.Tests.CameraFollowerTests" runstate="Runnable" testcasecount="11" result="Failed" site="Child" start-time="2025-08-05 06:29:39Z" end-time="2025-08-05 06:29:39Z" duration="0.164913" total="11" passed="8" failed="3" inconclusive="0" skipped="0" asserts="0">
            <properties />
            <failure>
              <message><![CDATA[One or more child tests had errors]]></message>
            </failure>
            <test-case id="1031" name="EnableBounds_TogglesBoundaryRestriction" fullname="MobileScrollingGame.Tests.CameraFollowerTests.EnableBounds_TogglesBoundaryRestriction" methodname="EnableBounds_TogglesBoundaryRestriction" classname="MobileScrollingGame.Tests.CameraFollowerTests" runstate="Runnable" seed="1503594286" result="Failed" start-time="2025-08-05 06:29:39Z" end-time="2025-08-05 06:29:39Z" duration="0.025169" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  禁用边界时摄像机应该能超出边界
  Expected: greater than 2.0f
  But was:  0.209307149f
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CameraFollowerTests.EnableBounds_TogglesBoundaryRestriction () [0x00088] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraFollowerTests.cs:235
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1024" name="EnableFollowing_DisablesFollowingWhenFalse" fullname="MobileScrollingGame.Tests.CameraFollowerTests.EnableFollowing_DisablesFollowingWhenFalse" methodname="EnableFollowing_DisablesFollowingWhenFalse" classname="MobileScrollingGame.Tests.CameraFollowerTests" runstate="Runnable" seed="353888655" result="Passed" start-time="2025-08-05 06:29:39Z" end-time="2025-08-05 06:29:39Z" duration="0.008466" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1026" name="GetCameraPosition_ReturnsCorrectPosition" fullname="MobileScrollingGame.Tests.CameraFollowerTests.GetCameraPosition_ReturnsCorrectPosition" methodname="GetCameraPosition_ReturnsCorrectPosition" classname="MobileScrollingGame.Tests.CameraFollowerTests" runstate="Runnable" seed="1648359353" result="Passed" start-time="2025-08-05 06:29:39Z" end-time="2025-08-05 06:29:39Z" duration="0.000558" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1023" name="SetCameraBounds_LimitsCameraMovement" fullname="MobileScrollingGame.Tests.CameraFollowerTests.SetCameraBounds_LimitsCameraMovement" methodname="SetCameraBounds_LimitsCameraMovement" classname="MobileScrollingGame.Tests.CameraFollowerTests" runstate="Runnable" seed="1169556089" result="Failed" start-time="2025-08-05 06:29:39Z" end-time="2025-08-05 06:29:39Z" duration="0.001395" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  摄像机不应超出右边界。位置: 0.2093071, 最大允许: -11.85015, 边界: (x:-5.00, y:-3.00, width:10.00, height:6.00), 摄像机宽度: 33.70031
  Expected: less than or equal to -11.7501526f
  But was:  0.209307149f
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CameraFollowerTests.SetCameraBounds_LimitsCameraMovement () [0x000ab] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraFollowerTests.cs:97
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1030" name="SetFollowSpeed_ChangesFollowSpeed" fullname="MobileScrollingGame.Tests.CameraFollowerTests.SetFollowSpeed_ChangesFollowSpeed" methodname="SetFollowSpeed_ChangesFollowSpeed" classname="MobileScrollingGame.Tests.CameraFollowerTests" runstate="Runnable" seed="2141727908" result="Passed" start-time="2025-08-05 06:29:39Z" end-time="2025-08-05 06:29:39Z" duration="0.001278" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1021" name="SetFollowTarget_SetsTargetCorrectly" fullname="MobileScrollingGame.Tests.CameraFollowerTests.SetFollowTarget_SetsTargetCorrectly" methodname="SetFollowTarget_SetsTargetCorrectly" classname="MobileScrollingGame.Tests.CameraFollowerTests" runstate="Runnable" seed="371028836" result="Passed" start-time="2025-08-05 06:29:39Z" end-time="2025-08-05 06:29:39Z" duration="0.004165" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1028" name="SetOffset_ChangesFollowOffset" fullname="MobileScrollingGame.Tests.CameraFollowerTests.SetOffset_ChangesFollowOffset" methodname="SetOffset_ChangesFollowOffset" classname="MobileScrollingGame.Tests.CameraFollowerTests" runstate="Runnable" seed="987362640" result="Passed" start-time="2025-08-05 06:29:39Z" end-time="2025-08-05 06:29:39Z" duration="0.000647" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1025" name="ShakeCamera_TriggersShakeEffect" fullname="MobileScrollingGame.Tests.CameraFollowerTests.ShakeCamera_TriggersShakeEffect" methodname="ShakeCamera_TriggersShakeEffect" classname="MobileScrollingGame.Tests.CameraFollowerTests" runstate="Runnable" seed="1480072186" result="Passed" start-time="2025-08-05 06:29:39Z" end-time="2025-08-05 06:29:39Z" duration="0.000541" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1029" name="SnapToTarget_MovesImmediatelyToTarget" fullname="MobileScrollingGame.Tests.CameraFollowerTests.SnapToTarget_MovesImmediatelyToTarget" methodname="SnapToTarget_MovesImmediatelyToTarget" classname="MobileScrollingGame.Tests.CameraFollowerTests" runstate="Runnable" seed="871229311" result="Passed" start-time="2025-08-05 06:29:39Z" end-time="2025-08-05 06:29:39Z" duration="0.000929" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1022" name="UpdateCameraPosition_FollowsTargetMovement" fullname="MobileScrollingGame.Tests.CameraFollowerTests.UpdateCameraPosition_FollowsTargetMovement" methodname="UpdateCameraPosition_FollowsTargetMovement" classname="MobileScrollingGame.Tests.CameraFollowerTests" runstate="Runnable" seed="51342491" result="Passed" start-time="2025-08-05 06:29:39Z" end-time="2025-08-05 06:29:39Z" duration="0.000501" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1027" name="UpdateCameraPosition_SmoothlyFollowsTarget" fullname="MobileScrollingGame.Tests.CameraFollowerTests.UpdateCameraPosition_SmoothlyFollowsTarget" methodname="UpdateCameraPosition_SmoothlyFollowsTarget" classname="MobileScrollingGame.Tests.CameraFollowerTests" runstate="Runnable" seed="1936416218" result="Failed" start-time="2025-08-05 06:29:39Z" end-time="2025-08-05 06:29:39Z" duration="0.098874" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  摄像机应该接近目标位置
  Expected: less than 1.0f
  But was:  3.83603001f
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CameraFollowerTests+<UpdateCameraPosition_SmoothlyFollowsTarget>d__12.MoveNext () [0x000f6] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraFollowerTests.cs:167
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
          </test-suite>
          <test-suite type="TestFixture" id="1032" name="CameraFollowerTestsFixed" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" testcasecount="13" result="Failed" site="Child" start-time="2025-08-05 06:29:39Z" end-time="2025-08-05 06:29:39Z" duration="0.098712" total="13" passed="10" failed="3" inconclusive="0" skipped="0" asserts="0">
            <properties />
            <failure>
              <message><![CDATA[One or more child tests had errors]]></message>
            </failure>
            <test-case id="1044" name="ComponentIntegrity_AllComponentsExist" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.ComponentIntegrity_AllComponentsExist" methodname="ComponentIntegrity_AllComponentsExist" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="1041478671" result="Passed" start-time="2025-08-05 06:29:39Z" end-time="2025-08-05 06:29:39Z" duration="0.002499" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1043" name="EnableBounds_TogglesBoundaryRestriction" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.EnableBounds_TogglesBoundaryRestriction" methodname="EnableBounds_TogglesBoundaryRestriction" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="189479432" result="Failed" start-time="2025-08-05 06:29:39Z" end-time="2025-08-05 06:29:39Z" duration="0.001128" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  禁用边界时摄像机应该能超出边界。摄像机位置: 0.03849785, 边界最大X: 2
  Expected: greater than 2.0f
  But was:  0.0384978466f
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CameraFollowerTestsFixed.EnableBounds_TogglesBoundaryRestriction () [0x00095] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraFollowerTestsFixed.cs:285
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1036" name="EnableFollowing_DisablesFollowingWhenFalse" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.EnableFollowing_DisablesFollowingWhenFalse" methodname="EnableFollowing_DisablesFollowingWhenFalse" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="1340195531" result="Passed" start-time="2025-08-05 06:29:39Z" end-time="2025-08-05 06:29:39Z" duration="0.000969" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1038" name="GetCameraPosition_ReturnsCorrectPosition" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.GetCameraPosition_ReturnsCorrectPosition" methodname="GetCameraPosition_ReturnsCorrectPosition" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="317461688" result="Passed" start-time="2025-08-05 06:29:39Z" end-time="2025-08-05 06:29:39Z" duration="0.000593" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1045" name="PublicMethods_DoNotThrowExceptions" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.PublicMethods_DoNotThrowExceptions" methodname="PublicMethods_DoNotThrowExceptions" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="1354259620" result="Passed" start-time="2025-08-05 06:29:39Z" end-time="2025-08-05 06:29:39Z" duration="0.001168" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1035" name="SetCameraBounds_LimitsCameraMovement" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.SetCameraBounds_LimitsCameraMovement" methodname="SetCameraBounds_LimitsCameraMovement" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="321455849" result="Failed" start-time="2025-08-05 06:29:39Z" end-time="2025-08-05 06:29:39Z" duration="0.000883" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  摄像机不应超出右边界。摄像机位置: 0.03849785, 预期最大X: -3.888889, 边界: (x:-5.00, y:-3.00, width:10.00, height:6.00), 摄像机尺寸: 17.77778x10
  Expected: less than or equal to -3.68888927f
  But was:  0.0384978466f
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CameraFollowerTestsFixed.SetCameraBounds_LimitsCameraMovement () [0x000be] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraFollowerTestsFixed.cs:125
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1042" name="SetFollowSpeed_UpdatesSpeedCorrectly" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.SetFollowSpeed_UpdatesSpeedCorrectly" methodname="SetFollowSpeed_UpdatesSpeedCorrectly" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="200866588" result="Passed" start-time="2025-08-05 06:29:39Z" end-time="2025-08-05 06:29:39Z" duration="0.000582" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1033" name="SetFollowTarget_SetsTargetCorrectly" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.SetFollowTarget_SetsTargetCorrectly" methodname="SetFollowTarget_SetsTargetCorrectly" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="1479390473" result="Passed" start-time="2025-08-05 06:29:39Z" end-time="2025-08-05 06:29:39Z" duration="0.000629" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1040" name="SetOffset_ChangesFollowOffset" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.SetOffset_ChangesFollowOffset" methodname="SetOffset_ChangesFollowOffset" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="392675635" result="Passed" start-time="2025-08-05 06:29:39Z" end-time="2025-08-05 06:29:39Z" duration="0.000551" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1037" name="ShakeCamera_DoesNotThrowException" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.ShakeCamera_DoesNotThrowException" methodname="ShakeCamera_DoesNotThrowException" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="1093353284" result="Passed" start-time="2025-08-05 06:29:39Z" end-time="2025-08-05 06:29:39Z" duration="0.000329" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1041" name="SnapToTarget_MovesImmediatelyToTarget" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.SnapToTarget_MovesImmediatelyToTarget" methodname="SnapToTarget_MovesImmediatelyToTarget" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="1705775969" result="Passed" start-time="2025-08-05 06:29:39Z" end-time="2025-08-05 06:29:39Z" duration="0.000510" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1034" name="UpdateCameraPosition_FollowsTargetMovement" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.UpdateCameraPosition_FollowsTargetMovement" methodname="UpdateCameraPosition_FollowsTargetMovement" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="2012728597" result="Failed" start-time="2025-08-05 06:29:39Z" end-time="2025-08-05 06:29:39Z" duration="0.000695" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  摄像机应该跟随目标向右移动。初始位置: 0, 新位置: 0.03849785, 移动距离: 0.03849785
  Expected: greater than 0.100000001f
  But was:  0.0384978466f
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CameraFollowerTestsFixed.UpdateCameraPosition_FollowsTargetMovement () [0x0009d] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraFollowerTestsFixed.cs:96
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1039" name="UpdateCameraPosition_SmoothlyFollowsTarget" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.UpdateCameraPosition_SmoothlyFollowsTarget" methodname="UpdateCameraPosition_SmoothlyFollowsTarget" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="*********" result="Passed" start-time="2025-08-05 06:29:39Z" end-time="2025-08-05 06:29:39Z" duration="0.071427" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[Saving results to: /Users/<USER>/Library/Application Support/DefaultCompany/2dMobile2/TestResults.xml
]]></output>
            </test-case>
          </test-suite>
        </test-suite>
      </test-suite>
    </test-suite>
  </test-suite>
</test-run>
<?xml version="1.0" encoding="utf-8"?>
<test-run id="2" testcasecount="24" result="Failed(Child)" total="24" passed="17" failed="7" inconclusive="0" skipped="0" asserts="0" engine-version="*******" clr-version="4.0.30319.42000" start-time="2025-08-05 06:13:28Z" end-time="2025-08-05 06:13:28Z" duration="0.2799692">
  <test-suite type="TestSuite" id="1317" name="2dMobile2" fullname="2dMobile2" runstate="Runnable" testcasecount="289" result="Failed" site="Child" start-time="2025-08-05 06:13:28Z" end-time="2025-08-05 06:13:28Z" duration="0.279969" total="24" passed="17" failed="7" inconclusive="0" skipped="0" asserts="0">
    <properties>
      <property name="platform" value="EditMode" />
    </properties>
    <failure>
      <message><![CDATA[One or more child tests had errors]]></message>
    </failure>
    <test-suite type="Assembly" id="1310" name="MobileScrollingGame.Tests.dll" fullname="/Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Library/ScriptAssemblies/MobileScrollingGame.Tests.dll" runstate="Runnable" testcasecount="289" result="Failed" site="Child" start-time="2025-08-05 06:13:28Z" end-time="2025-08-05 06:13:28Z" duration="0.264151" total="24" passed="17" failed="7" inconclusive="0" skipped="0" asserts="0">
      <properties>
        <property name="_PID" value="4631" />
        <property name="_APPDOMAIN" value="Unity Child Domain" />
        <property name="platform" value="EditMode" />
        <property name="EditorOnly" value="True" />
      </properties>
      <failure>
        <message><![CDATA[One or more child tests had errors]]></message>
      </failure>
      <test-suite type="TestSuite" id="1311" name="MobileScrollingGame" fullname="MobileScrollingGame" runstate="Runnable" testcasecount="289" result="Failed" site="Child" start-time="2025-08-05 06:13:28Z" end-time="2025-08-05 06:13:28Z" duration="0.263454" total="24" passed="17" failed="7" inconclusive="0" skipped="0" asserts="0">
        <properties />
        <failure>
          <message><![CDATA[One or more child tests had errors]]></message>
        </failure>
        <test-suite type="TestSuite" id="1312" name="Tests" fullname="MobileScrollingGame.Tests" runstate="Runnable" testcasecount="289" result="Failed" site="Child" start-time="2025-08-05 06:13:28Z" end-time="2025-08-05 06:13:28Z" duration="0.262890" total="24" passed="17" failed="7" inconclusive="0" skipped="0" asserts="0">
          <properties />
          <failure>
            <message><![CDATA[One or more child tests had errors]]></message>
          </failure>
          <test-suite type="TestFixture" id="1020" name="CameraFollowerTests" fullname="MobileScrollingGame.Tests.CameraFollowerTests" classname="MobileScrollingGame.Tests.CameraFollowerTests" runstate="Runnable" testcasecount="11" result="Failed" site="Child" start-time="2025-08-05 06:13:28Z" end-time="2025-08-05 06:13:28Z" duration="0.165422" total="11" passed="7" failed="4" inconclusive="0" skipped="0" asserts="0">
            <properties />
            <failure>
              <message><![CDATA[One or more child tests had errors]]></message>
            </failure>
            <test-case id="1031" name="EnableBounds_TogglesBoundaryRestriction" fullname="MobileScrollingGame.Tests.CameraFollowerTests.EnableBounds_TogglesBoundaryRestriction" methodname="EnableBounds_TogglesBoundaryRestriction" classname="MobileScrollingGame.Tests.CameraFollowerTests" runstate="Runnable" seed="794285046" result="Failed" start-time="2025-08-05 06:13:28Z" end-time="2025-08-05 06:13:28Z" duration="0.027995" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  禁用边界时摄像机应该能超出边界
  Expected: greater than 2.0f
  But was:  0.222474664f
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CameraFollowerTests.EnableBounds_TogglesBoundaryRestriction () [0x00088] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraFollowerTests.cs:235
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1024" name="EnableFollowing_DisablesFollowingWhenFalse" fullname="MobileScrollingGame.Tests.CameraFollowerTests.EnableFollowing_DisablesFollowingWhenFalse" methodname="EnableFollowing_DisablesFollowingWhenFalse" classname="MobileScrollingGame.Tests.CameraFollowerTests" runstate="Runnable" seed="2140174652" result="Failed" start-time="2025-08-05 06:13:28Z" end-time="2025-08-05 06:13:28Z" duration="0.007359" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  禁用跟随时摄像机不应移动
  Expected: (0.00, 1.00, -10.00)
  But was:  (0.22, 1.00, -10.00)
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CameraFollowerTests.EnableFollowing_DisablesFollowingWhenFalse () [0x00072] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraFollowerTests.cs:115
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1026" name="GetCameraPosition_ReturnsCorrectPosition" fullname="MobileScrollingGame.Tests.CameraFollowerTests.GetCameraPosition_ReturnsCorrectPosition" methodname="GetCameraPosition_ReturnsCorrectPosition" classname="MobileScrollingGame.Tests.CameraFollowerTests" runstate="Runnable" seed="924199991" result="Passed" start-time="2025-08-05 06:13:28Z" end-time="2025-08-05 06:13:28Z" duration="0.002965" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1023" name="SetCameraBounds_LimitsCameraMovement" fullname="MobileScrollingGame.Tests.CameraFollowerTests.SetCameraBounds_LimitsCameraMovement" methodname="SetCameraBounds_LimitsCameraMovement" classname="MobileScrollingGame.Tests.CameraFollowerTests" runstate="Runnable" seed="1815394493" result="Failed" start-time="2025-08-05 06:13:28Z" end-time="2025-08-05 06:13:28Z" duration="0.001365" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  摄像机不应超出右边界。位置: 0.2224747, 最大允许: -11.85015, 边界: (x:-5.00, y:-3.00, width:10.00, height:6.00), 摄像机宽度: 33.70031
  Expected: less than or equal to -11.7501526f
  But was:  0.222474664f
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CameraFollowerTests.SetCameraBounds_LimitsCameraMovement () [0x000ab] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraFollowerTests.cs:97
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1030" name="SetFollowSpeed_ChangesFollowSpeed" fullname="MobileScrollingGame.Tests.CameraFollowerTests.SetFollowSpeed_ChangesFollowSpeed" methodname="SetFollowSpeed_ChangesFollowSpeed" classname="MobileScrollingGame.Tests.CameraFollowerTests" runstate="Runnable" seed="1726655067" result="Passed" start-time="2025-08-05 06:13:28Z" end-time="2025-08-05 06:13:28Z" duration="0.001434" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1021" name="SetFollowTarget_SetsTargetCorrectly" fullname="MobileScrollingGame.Tests.CameraFollowerTests.SetFollowTarget_SetsTargetCorrectly" methodname="SetFollowTarget_SetsTargetCorrectly" classname="MobileScrollingGame.Tests.CameraFollowerTests" runstate="Runnable" seed="972641930" result="Passed" start-time="2025-08-05 06:13:28Z" end-time="2025-08-05 06:13:28Z" duration="0.004524" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1028" name="SetOffset_ChangesFollowOffset" fullname="MobileScrollingGame.Tests.CameraFollowerTests.SetOffset_ChangesFollowOffset" methodname="SetOffset_ChangesFollowOffset" classname="MobileScrollingGame.Tests.CameraFollowerTests" runstate="Runnable" seed="880388285" result="Passed" start-time="2025-08-05 06:13:28Z" end-time="2025-08-05 06:13:28Z" duration="0.000708" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1025" name="ShakeCamera_TriggersShakeEffect" fullname="MobileScrollingGame.Tests.CameraFollowerTests.ShakeCamera_TriggersShakeEffect" methodname="ShakeCamera_TriggersShakeEffect" classname="MobileScrollingGame.Tests.CameraFollowerTests" runstate="Runnable" seed="301858923" result="Passed" start-time="2025-08-05 06:13:28Z" end-time="2025-08-05 06:13:28Z" duration="0.000455" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1029" name="SnapToTarget_MovesImmediatelyToTarget" fullname="MobileScrollingGame.Tests.CameraFollowerTests.SnapToTarget_MovesImmediatelyToTarget" methodname="SnapToTarget_MovesImmediatelyToTarget" classname="MobileScrollingGame.Tests.CameraFollowerTests" runstate="Runnable" seed="857271496" result="Passed" start-time="2025-08-05 06:13:28Z" end-time="2025-08-05 06:13:28Z" duration="0.000621" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1022" name="UpdateCameraPosition_FollowsTargetMovement" fullname="MobileScrollingGame.Tests.CameraFollowerTests.UpdateCameraPosition_FollowsTargetMovement" methodname="UpdateCameraPosition_FollowsTargetMovement" classname="MobileScrollingGame.Tests.CameraFollowerTests" runstate="Runnable" seed="18820062" result="Passed" start-time="2025-08-05 06:13:28Z" end-time="2025-08-05 06:13:28Z" duration="0.000425" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1027" name="UpdateCameraPosition_SmoothlyFollowsTarget" fullname="MobileScrollingGame.Tests.CameraFollowerTests.UpdateCameraPosition_SmoothlyFollowsTarget" methodname="UpdateCameraPosition_SmoothlyFollowsTarget" classname="MobileScrollingGame.Tests.CameraFollowerTests" runstate="Runnable" seed="1146750563" result="Failed" start-time="2025-08-05 06:13:28Z" end-time="2025-08-05 06:13:28Z" duration="0.096808" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  摄像机应该接近目标位置
  Expected: less than 1.0f
  But was:  3.83297396f
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CameraFollowerTests+<UpdateCameraPosition_SmoothlyFollowsTarget>d__12.MoveNext () [0x000f6] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraFollowerTests.cs:167
at UnityEngine.TestTools.TestEnumerator+<Execute>d__7.MoveNext () [0x0003a] in ./Library/PackageCache/com.unity.test-framework@f6ed7fd5ec8f/UnityEngine.TestRunner/NUnitExtensions/Attributes/TestEnumerator.cs:44
]]></stack-trace>
              </failure>
            </test-case>
          </test-suite>
          <test-suite type="TestFixture" id="1032" name="CameraFollowerTestsFixed" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" testcasecount="13" result="Failed" site="Child" start-time="2025-08-05 06:13:28Z" end-time="2025-08-05 06:13:28Z" duration="0.094310" total="13" passed="10" failed="3" inconclusive="0" skipped="0" asserts="0">
            <properties />
            <failure>
              <message><![CDATA[One or more child tests had errors]]></message>
            </failure>
            <test-case id="1044" name="ComponentIntegrity_AllComponentsExist" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.ComponentIntegrity_AllComponentsExist" methodname="ComponentIntegrity_AllComponentsExist" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="552799005" result="Passed" start-time="2025-08-05 06:13:28Z" end-time="2025-08-05 06:13:28Z" duration="0.001676" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1043" name="EnableBounds_TogglesBoundaryRestriction" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.EnableBounds_TogglesBoundaryRestriction" methodname="EnableBounds_TogglesBoundaryRestriction" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="1053616387" result="Failed" start-time="2025-08-05 06:13:28Z" end-time="2025-08-05 06:13:28Z" duration="0.000732" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  禁用边界时摄像机应该能超出边界。摄像机位置: 0.03288035, 边界最大X: 2
  Expected: greater than 2.0f
  But was:  0.0328803472f
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CameraFollowerTestsFixed.EnableBounds_TogglesBoundaryRestriction () [0x00095] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraFollowerTestsFixed.cs:285
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1036" name="EnableFollowing_DisablesFollowingWhenFalse" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.EnableFollowing_DisablesFollowingWhenFalse" methodname="EnableFollowing_DisablesFollowingWhenFalse" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="1460421718" result="Passed" start-time="2025-08-05 06:13:28Z" end-time="2025-08-05 06:13:28Z" duration="0.000595" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1038" name="GetCameraPosition_ReturnsCorrectPosition" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.GetCameraPosition_ReturnsCorrectPosition" methodname="GetCameraPosition_ReturnsCorrectPosition" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="569373649" result="Passed" start-time="2025-08-05 06:13:28Z" end-time="2025-08-05 06:13:28Z" duration="0.000448" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1045" name="PublicMethods_DoNotThrowExceptions" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.PublicMethods_DoNotThrowExceptions" methodname="PublicMethods_DoNotThrowExceptions" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="381063054" result="Passed" start-time="2025-08-05 06:13:28Z" end-time="2025-08-05 06:13:28Z" duration="0.001195" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1035" name="SetCameraBounds_LimitsCameraMovement" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.SetCameraBounds_LimitsCameraMovement" methodname="SetCameraBounds_LimitsCameraMovement" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="73087789" result="Failed" start-time="2025-08-05 06:13:28Z" end-time="2025-08-05 06:13:28Z" duration="0.000814" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  摄像机不应超出右边界。摄像机位置: 0.03288035, 预期最大X: -3.888889, 边界: (x:-5.00, y:-3.00, width:10.00, height:6.00), 摄像机尺寸: 17.77778x10
  Expected: less than or equal to -3.68888927f
  But was:  0.0328803472f
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CameraFollowerTestsFixed.SetCameraBounds_LimitsCameraMovement () [0x000be] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraFollowerTestsFixed.cs:125
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1042" name="SetFollowSpeed_UpdatesSpeedCorrectly" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.SetFollowSpeed_UpdatesSpeedCorrectly" methodname="SetFollowSpeed_UpdatesSpeedCorrectly" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="31285914" result="Passed" start-time="2025-08-05 06:13:28Z" end-time="2025-08-05 06:13:28Z" duration="0.000583" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1033" name="SetFollowTarget_SetsTargetCorrectly" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.SetFollowTarget_SetsTargetCorrectly" methodname="SetFollowTarget_SetsTargetCorrectly" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="1535387334" result="Passed" start-time="2025-08-05 06:13:28Z" end-time="2025-08-05 06:13:28Z" duration="0.000625" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1040" name="SetOffset_ChangesFollowOffset" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.SetOffset_ChangesFollowOffset" methodname="SetOffset_ChangesFollowOffset" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="2040931737" result="Passed" start-time="2025-08-05 06:13:28Z" end-time="2025-08-05 06:13:28Z" duration="0.000518" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1037" name="ShakeCamera_DoesNotThrowException" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.ShakeCamera_DoesNotThrowException" methodname="ShakeCamera_DoesNotThrowException" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="1251737471" result="Passed" start-time="2025-08-05 06:13:28Z" end-time="2025-08-05 06:13:28Z" duration="0.000369" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1041" name="SnapToTarget_MovesImmediatelyToTarget" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.SnapToTarget_MovesImmediatelyToTarget" methodname="SnapToTarget_MovesImmediatelyToTarget" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="1348123809" result="Passed" start-time="2025-08-05 06:13:28Z" end-time="2025-08-05 06:13:28Z" duration="0.000530" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
            </test-case>
            <test-case id="1034" name="UpdateCameraPosition_FollowsTargetMovement" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.UpdateCameraPosition_FollowsTargetMovement" methodname="UpdateCameraPosition_FollowsTargetMovement" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="1053625326" result="Failed" start-time="2025-08-05 06:13:28Z" end-time="2025-08-05 06:13:28Z" duration="0.000687" asserts="0">
              <properties>
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <failure>
                <message><![CDATA[  摄像机应该跟随目标向右移动。初始位置: 0, 新位置: 0.03288035, 移动距离: 0.03288035
  Expected: greater than 0.100000001f
  But was:  0.0328803472f
]]></message>
                <stack-trace><![CDATA[at MobileScrollingGame.Tests.CameraFollowerTestsFixed.UpdateCameraPosition_FollowsTargetMovement () [0x0009d] in /Users/<USER>/workshop/unity3d/github/mobile_2d/2dMobile2/Assets/Scripts/Tests/CameraFollowerTestsFixed.cs:96
]]></stack-trace>
              </failure>
            </test-case>
            <test-case id="1039" name="UpdateCameraPosition_SmoothlyFollowsTarget" fullname="MobileScrollingGame.Tests.CameraFollowerTestsFixed.UpdateCameraPosition_SmoothlyFollowsTarget" methodname="UpdateCameraPosition_SmoothlyFollowsTarget" classname="MobileScrollingGame.Tests.CameraFollowerTestsFixed" runstate="Runnable" seed="2079856" result="Passed" start-time="2025-08-05 06:13:28Z" end-time="2025-08-05 06:13:28Z" duration="0.067944" asserts="0">
              <properties>
                <property name="_JOINTYPE" value="UnityCombinatorial" />
                <property name="retryIteration" value="0" />
                <property name="repeatIteration" value="0" />
              </properties>
              <output><![CDATA[Saving results to: /Users/<USER>/Library/Application Support/DefaultCompany/2dMobile2/TestResults.xml
]]></output>
            </test-case>
          </test-suite>
        </test-suite>
      </test-suite>
    </test-suite>
  </test-suite>
</test-run>
using UnityEngine;

namespace MobileScrollingGame.Core
{
    /// <summary>
    /// 输入处理的接口
    /// 处理移动设备触控输入并转换为游戏动作
    /// </summary>
    public interface IInputHandler
    {
        /// <summary>
        /// 获取当前移动输入
        /// </summary>
        /// <returns>标准化的移动向量</returns>
        Vector2 GetMovementInput();
        
        /// <summary>
        /// 获取跳跃输入状态
        /// </summary>
        /// <returns>如果按下跳跃则返回true</returns>
        bool GetJumpInput();
        
        /// <summary>
        /// 获取动作输入状态
        /// </summary>
        /// <returns>如果按下动作按钮则返回true</returns>
        bool GetActionInput();
        
        /// <summary>
        /// 启用或禁用输入处理
        /// </summary>
        /// <param name="enabled">是否启用输入</param>
        void EnableInput(bool enabled);
        
        /// <summary>
        /// 检查是否有任何活动输入
        /// </summary>
        /// <returns>如果有活动输入则返回true</returns>
        bool HasActiveInput();
        
        /// <summary>
        /// 重置所有输入状态
        /// </summary>
        void ResetInputs();
    }
}
using UnityEngine;

namespace MobileScrollingGame.Core
{
    /// <summary>
    /// 角色控制和移动的接口
    /// 处理玩家角色动作和状态管理
    /// </summary>
    public interface ICharacterController
    {
        /// <summary>
        /// 使角色向指定方向移动
        /// </summary>
        /// <param name="direction">标准化的移动方向</param>
        void Move(Vector2 direction);
        
        /// <summary>
        /// 如果角色在地面上则使其跳跃
        /// </summary>
        void Jump();
        
        /// <summary>
        /// 执行角色动作（攻击/交互）
        /// </summary>
        void PerformAction();
        
        /// <summary>
        /// 对角色造成伤害
        /// </summary>
        /// <param name="damage">要造成的伤害值</param>
        void TakeDamage(int damage);
        
        /// <summary>
        /// 设置角色动画状态
        /// </summary>
        /// <param name="animationName">要播放的动画名称</param>
        void SetAnimation(string animationName);
        
        /// <summary>
        /// 没有输入时返回空闲状态
        /// </summary>
        void SetIdleState();
        
        /// <summary>
        /// 获取当前角色位置
        /// </summary>
        Vector3 GetPosition();
        
        /// <summary>
        /// 检查角色当前是否在地面上
        /// </summary>
        bool IsGrounded();
        
        /// <summary>
        /// 获取当前角色生命值
        /// </summary>
        int GetHealth();
    }
}
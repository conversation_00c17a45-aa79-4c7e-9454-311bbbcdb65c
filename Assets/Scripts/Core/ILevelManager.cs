using UnityEngine;
using System.Collections;

namespace MobileScrollingGame.Core
{
    /// <summary>
    /// 关卡管理和进度的接口
    /// 处理关卡加载、检查点和完成
    /// </summary>
    public interface ILevelManager
    {
        /// <summary>
        /// 异步加载关卡
        /// </summary>
        /// <param name="levelIndex">要加载的关卡索引</param>
        IEnumerator LoadLevel(int levelIndex);
        
        /// <summary>
        /// 在检查点保存当前游戏进度
        /// </summary>
        /// <param name="checkpointPosition">检查点位置</param>
        void SaveCheckpoint(Vector3 checkpointPosition);
        
        /// <summary>
        /// 在最后一个检查点重生玩家
        /// </summary>
        void RespawnAtCheckpoint();
        
        /// <summary>
        /// 完成当前关卡并进入下一关
        /// </summary>
        void CompleteLevel();
        
        /// <summary>
        /// 获取当前关卡索引
        /// </summary>
        int GetCurrentLevel();
        
        /// <summary>
        /// 检查关卡是否已完成
        /// </summary>
        bool IsLevelCompleted(int levelIndex);
        
        /// <summary>
        /// 重置关卡进度
        /// </summary>
        void ResetLevel();
    }
}
using UnityEngine;

namespace MobileScrollingGame.Core
{
    /// <summary>
    /// 音频管理的接口
    /// 处理音效和背景音乐
    /// </summary>
    public interface IAudioManager
    {
        /// <summary>
        /// 播放音效
        /// </summary>
        /// <param name="soundName">要播放的音效名称</param>
        void PlaySoundEffect(string soundName);
        
        /// <summary>
        /// 播放背景音乐
        /// </summary>
        /// <param name="musicName">音乐轨道名称</param>
        void PlayBackgroundMusic(string musicName);
        
        /// <summary>
        /// 停止背景音乐
        /// </summary>
        void StopBackgroundMusic();
        
        /// <summary>
        /// 设置主音量
        /// </summary>
        /// <param name="volume">音量级别（0-1）</param>
        void SetMasterVolume(float volume);
        
        /// <summary>
        /// 设置音效音量
        /// </summary>
        /// <param name="volume">音量级别（0-1）</param>
        void SetSFXVolume(float volume);
        
        /// <summary>
        /// 设置音乐音量
        /// </summary>
        /// <param name="volume">音量级别（0-1）</param>
        void SetMusicVolume(float volume);
    }
}
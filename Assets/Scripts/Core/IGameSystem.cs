namespace MobileScrollingGame.Core
{
    /// <summary>
    /// 所有游戏系统的基础接口
    /// 提供通用的生命周期方法
    /// </summary>
    public interface IGameSystem
    {
        /// <summary>
        /// 初始化系统
        /// </summary>
        void Initialize();
        
        /// <summary>
        /// 更新系统（每帧调用）
        /// </summary>
        void UpdateSystem();
        
        /// <summary>
        /// 关闭系统并清理资源
        /// </summary>
        void Shutdown();
        
        /// <summary>
        /// 获取系统是否已初始化
        /// </summary>
        bool IsInitialized { get; }
    }
}
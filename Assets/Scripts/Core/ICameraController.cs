using UnityEngine;

namespace MobileScrollingGame.Core
{
    /// <summary>
    /// 相机控制和跟随行为的接口
    /// 管理2D滚动的相机移动和边界
    /// </summary>
    public interface ICameraController
    {
        /// <summary>
        /// 设置相机要跟随的目标
        /// </summary>
        /// <param name="target">要跟随的Transform</param>
        void SetFollowTarget(Transform target);
        
        /// <summary>
        /// 更新相机位置以平滑跟随目标
        /// </summary>
        void UpdateCameraPosition();
        
        /// <summary>
        /// 设置相机边界以防止滚动超出关卡限制
        /// </summary>
        /// <param name="bounds">边界矩形</param>
        void SetCameraBounds(Rect bounds);
        
        /// <summary>
        /// 触发相机震动效果
        /// </summary>
        /// <param name="intensity">震动强度</param>
        /// <param name="duration">震动持续时间（秒）</param>
        void ShakeCamera(float intensity, float duration);
        
        /// <summary>
        /// 获取当前相机位置
        /// </summary>
        Vector3 GetCameraPosition();
        
        /// <summary>
        /// 启用或禁用相机跟随
        /// </summary>
        void EnableFollowing(bool enabled);
    }
}
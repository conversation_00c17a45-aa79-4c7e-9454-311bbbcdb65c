using UnityEngine;

namespace MobileScrollingGame.Core
{
    /// <summary>
    /// UI管理的接口
    /// 处理游戏UI、菜单和HUD元素
    /// </summary>
    public interface IUIManager
    {
        /// <summary>
        /// 显示游戏HUD
        /// </summary>
        void ShowGameHUD();
        
        /// <summary>
        /// 隐藏游戏HUD
        /// </summary>
        void HideGameHUD();
        
        /// <summary>
        /// 显示暂停菜单
        /// </summary>
        void ShowPauseMenu();
        
        /// <summary>
        /// 隐藏暂停菜单
        /// </summary>
        void HidePauseMenu();
        
        /// <summary>
        /// 显示游戏结束界面
        /// </summary>
        void ShowGameOverScreen();
        
        /// <summary>
        /// 显示关卡完成界面
        /// </summary>
        void ShowLevelCompleteScreen();
        
        /// <summary>
        /// 更新生命值显示
        /// </summary>
        /// <param name="currentHealth">当前生命值</param>
        /// <param name="maxHealth">最大生命值</param>
        void UpdateHealthDisplay(int currentHealth, int maxHealth);
        
        /// <summary>
        /// 更新分数显示
        /// </summary>
        /// <param name="score">当前分数</param>
        void UpdateScoreDisplay(int score);
        
        /// <summary>
        /// 显示反馈消息
        /// </summary>
        /// <param name="message">要显示的消息</param>
        void ShowFeedbackMessage(string message);
    }
}
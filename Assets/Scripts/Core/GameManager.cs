using UnityEngine;
using System.Collections;

namespace MobileScrollingGame.Core
{
    /// <summary>
    /// 协调所有游戏系统的中央游戏管理器
    /// 实现单例模式以供全局访问
    /// </summary>
    public class GameManager : MonoBehaviour
    {
        [Header("游戏系统")]
        [SerializeField] private bool initializeOnStart = true;
        
        // 单例实例
        private static GameManager _instance;
        public static GameManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindFirstObjectByType<GameManager>();
                    if (_instance == null)
                    {
                        GameObject gameManagerObject = new GameObject("GameManager");
                        _instance = gameManagerObject.AddComponent<GameManager>();
                        DontDestroyOnLoad(gameManagerObject);
                    }
                }
                return _instance;
            }
        }
        
        // 系统引用
        private IInputHandler _inputHandler;
        private ICharacterController _characterController;
        private ICameraController _cameraController;
        private ILevelManager _levelManager;
        private IAudioManager _audioManager;
        
        // 游戏状态
        [Header("游戏状态")]
        public bool isGamePaused = false;
        public bool isGameStarted = false;
        
        // 事件
        public System.Action OnGameStart;
        public System.Action OnGamePause;
        public System.Action OnGameResume;
        public System.Action OnGameOver;
        
        private void Awake()
        {
            // 确保单例模式
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else if (_instance != this)
            {
                Destroy(gameObject);
                return;
            }
            
            InitializeSystems();
        }
        
        private void Start()
        {
            if (initializeOnStart)
            {
                StartGame();
            }
        }
        
        /// <summary>
        /// 初始化所有游戏系统
        /// </summary>
        private void InitializeSystems()
        {
            Debug.Log("GameManager: 正在初始化游戏系统...");
            
            // 系统将在运行时注入或查找
            // 这提供了一个中央协调点
        }
        
        /// <summary>
        /// 注册输入处理系统
        /// </summary>
        public void RegisterInputHandler(IInputHandler inputHandler)
        {
            _inputHandler = inputHandler;
            Debug.Log("GameManager: 输入处理器已注册");
        }
        
        /// <summary>
        /// 注册角色控制器系统
        /// </summary>
        public void RegisterCharacterController(ICharacterController characterController)
        {
            _characterController = characterController;
            Debug.Log("GameManager: 角色控制器已注册");
        }
        
        /// <summary>
        /// 注册相机控制器系统
        /// </summary>
        public void RegisterCameraController(ICameraController cameraController)
        {
            _cameraController = cameraController;
            Debug.Log("GameManager: 相机控制器已注册");
        }
        
        /// <summary>
        /// 注册关卡管理器系统
        /// </summary>
        public void RegisterLevelManager(ILevelManager levelManager)
        {
            _levelManager = levelManager;
            Debug.Log("GameManager: 关卡管理器已注册");
        }
        
        /// <summary>
        /// 注册音频管理器系统
        /// </summary>
        public void RegisterAudioManager(IAudioManager audioManager)
        {
            _audioManager = audioManager;
            Debug.Log("GameManager: 音频管理器已注册");
        }
        
        /// <summary>
        /// 开始游戏
        /// </summary>
        public void StartGame()
        {
            if (!isGameStarted)
            {
                isGameStarted = true;
                isGamePaused = false;
                
                Debug.Log("GameManager: 游戏开始");
                OnGameStart?.Invoke();
                
                // 游戏开始时启用输入
                _inputHandler?.EnableInput(true);
            }
        }
        
        /// <summary>
        /// 暂停游戏
        /// </summary>
        public void PauseGame()
        {
            if (isGameStarted && !isGamePaused)
            {
                isGamePaused = true;
                Time.timeScale = 0f;
                
                Debug.Log("GameManager: 游戏暂停");
                OnGamePause?.Invoke();
                
                // 暂停时禁用输入
                _inputHandler?.EnableInput(false);
            }
        }
        
        /// <summary>
        /// 恢复游戏
        /// </summary>
        public void ResumeGame()
        {
            if (isGameStarted && isGamePaused)
            {
                isGamePaused = false;
                Time.timeScale = 1f;
                
                Debug.Log("GameManager: 游戏恢复");
                OnGameResume?.Invoke();
                
                // 恢复时重新启用输入
                _inputHandler?.EnableInput(true);
            }
        }
        
        /// <summary>
        /// 结束游戏
        /// </summary>
        public void GameOver()
        {
            if (isGameStarted)
            {
                isGameStarted = false;
                
                Debug.Log("GameManager: 游戏结束");
                OnGameOver?.Invoke();
                
                // 游戏结束时禁用输入
                _inputHandler?.EnableInput(false);
            }
        }
        
        /// <summary>
        /// 重新开始当前关卡
        /// </summary>
        public void RestartLevel()
        {
            if (_levelManager != null)
            {
                _levelManager.ResetLevel();
                StartGame();
            }
        }
        
        /// <summary>
        /// 获取已注册的输入处理器
        /// </summary>
        public IInputHandler GetInputHandler()
        {
            return _inputHandler;
        }
        
        /// <summary>
        /// 获取已注册的角色控制器
        /// </summary>
        public ICharacterController GetCharacterController()
        {
            return _characterController;
        }
        
        /// <summary>
        /// 获取已注册的相机控制器
        /// </summary>
        public ICameraController GetCameraController()
        {
            return _cameraController;
        }
        
        /// <summary>
        /// 获取已注册的关卡管理器
        /// </summary>
        public ILevelManager GetLevelManager()
        {
            return _levelManager;
        }
        
        /// <summary>
        /// 获取已注册的音频管理器
        /// </summary>
        public IAudioManager GetAudioManager()
        {
            return _audioManager;
        }
        
        /// <summary>
        /// 处理应用程序暂停（移动设备特定）
        /// </summary>
        private void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus)
            {
                PauseGame();
            }
            else
            {
                // 不自动恢复，让玩家选择
                Debug.Log("GameManager: 应用程序恢复，等待玩家输入");
            }
        }
        
        /// <summary>
        /// 处理应用程序焦点（移动设备特定）
        /// </summary>
        private void OnApplicationFocus(bool hasFocus)
        {
            if (!hasFocus)
            {
                PauseGame();
            }
        }
        
        private void OnDestroy()
        {
            if (_instance == this)
            {
                _instance = null;
            }
        }
    }
}
using UnityEngine;
using System.Collections.Generic;

namespace MobileScrollingGame.Player
{
    /// <summary>
    /// 动画状态枚举
    /// </summary>
    public enum AnimationState
    {
        Idle,
        Walking,
        Jumping,
        Falling,
        Landing,
        Action,
        Hurt,
        Death
    }
    
    /// <summary>
    /// 动画状态数据
    /// </summary>
    [System.Serializable]
    public class AnimationStateData
    {
        public AnimationState state;
        public string stateName;
        public string triggerName;
        public bool isLooping;
        public float transitionDuration;
        public int priority; // 优先级，数值越高优先级越高
        
        public AnimationStateData(AnimationState state, string stateName, string triggerName = "", 
            bool isLooping = true, float transitionDuration = 0.1f, int priority = 0)
        {
            this.state = state;
            this.stateName = stateName;
            this.triggerName = triggerName;
            this.isLooping = isLooping;
            this.transitionDuration = transitionDuration;
            this.priority = priority;
        }
    }
    
    /// <summary>
    /// 动画状态机 - 管理角色动画状态转换逻辑
    /// </summary>
    [System.Serializable]
    public class AnimationStateMachine
    {
        [Header("状态配置")]
        [SerializeField] private List<AnimationStateData> stateDataList;
        
        // 状态跟踪
        private AnimationState currentState = AnimationState.Idle;
        private AnimationState previousState = AnimationState.Idle;
        private Dictionary<AnimationState, AnimationStateData> stateDataMap;
        
        // 状态转换规则
        private Dictionary<AnimationState, List<AnimationState>> allowedTransitions;
        
        // 事件
        public System.Action<AnimationState, AnimationState> OnStateChanged;
        
        public AnimationState CurrentState => currentState;
        public AnimationState PreviousState => previousState;
        
        /// <summary>
        /// 初始化状态机
        /// </summary>
        public void Initialize()
        {
            InitializeStateData();
            InitializeTransitionRules();
        }
        
        /// <summary>
        /// 初始化状态数据
        /// </summary>
        private void InitializeStateData()
        {
            stateDataMap = new Dictionary<AnimationState, AnimationStateData>();
            
            // 如果没有配置数据，使用默认配置
            if (stateDataList == null || stateDataList.Count == 0)
            {
                CreateDefaultStateData();
            }
            
            // 构建状态数据映射
            foreach (var stateData in stateDataList)
            {
                if (!stateDataMap.ContainsKey(stateData.state))
                {
                    stateDataMap[stateData.state] = stateData;
                }
            }
        }
        
        /// <summary>
        /// 创建默认状态数据
        /// </summary>
        private void CreateDefaultStateData()
        {
            stateDataList = new List<AnimationStateData>
            {
                new AnimationStateData(AnimationState.Idle, "Idle", "", true, 0.1f, 0),
                new AnimationStateData(AnimationState.Walking, "Walking", "", true, 0.1f, 1),
                new AnimationStateData(AnimationState.Jumping, "Jumping", "Jump", false, 0.05f, 3),
                new AnimationStateData(AnimationState.Falling, "Falling", "", true, 0.1f, 2),
                new AnimationStateData(AnimationState.Landing, "Landing", "Land", false, 0.05f, 4),
                new AnimationStateData(AnimationState.Action, "Action", "Action", false, 0.05f, 5),
                new AnimationStateData(AnimationState.Hurt, "Hurt", "Hurt", false, 0.05f, 6),
                new AnimationStateData(AnimationState.Death, "Death", "Death", false, 0.1f, 10)
            };
        }
        
        /// <summary>
        /// 初始化状态转换规则
        /// </summary>
        private void InitializeTransitionRules()
        {
            allowedTransitions = new Dictionary<AnimationState, List<AnimationState>>
            {
                [AnimationState.Idle] = new List<AnimationState> 
                { 
                    AnimationState.Walking, AnimationState.Jumping, AnimationState.Action, AnimationState.Hurt 
                },
                [AnimationState.Walking] = new List<AnimationState> 
                { 
                    AnimationState.Idle, AnimationState.Jumping, AnimationState.Action, AnimationState.Hurt 
                },
                [AnimationState.Jumping] = new List<AnimationState> 
                { 
                    AnimationState.Falling, AnimationState.Landing, AnimationState.Hurt 
                },
                [AnimationState.Falling] = new List<AnimationState> 
                { 
                    AnimationState.Landing, AnimationState.Hurt 
                },
                [AnimationState.Landing] = new List<AnimationState> 
                { 
                    AnimationState.Idle, AnimationState.Walking, AnimationState.Hurt 
                },
                [AnimationState.Action] = new List<AnimationState> 
                { 
                    AnimationState.Idle, AnimationState.Walking, AnimationState.Hurt 
                },
                [AnimationState.Hurt] = new List<AnimationState> 
                { 
                    AnimationState.Idle, AnimationState.Death 
                },
                [AnimationState.Death] = new List<AnimationState>() // 死亡状态不能转换到其他状态
            };
        }
        
        /// <summary>
        /// 尝试转换到新状态
        /// </summary>
        public bool TryTransitionTo(AnimationState newState)
        {
            // 检查是否是相同状态
            if (currentState == newState)
            {
                return false;
            }
            
            // 检查转换是否被允许
            if (!IsTransitionAllowed(currentState, newState))
            {
                Debug.LogWarning($"AnimationStateMachine: 不允许从 {currentState} 转换到 {newState}");
                return false;
            }
            
            // 检查优先级
            if (!CheckStatePriority(newState))
            {
                return false;
            }
            
            // 执行状态转换
            previousState = currentState;
            currentState = newState;
            
            OnStateChanged?.Invoke(previousState, currentState);
            
            return true;
        }
        
        /// <summary>
        /// 强制转换到新状态（忽略转换规则）
        /// </summary>
        public void ForceTransitionTo(AnimationState newState)
        {
            if (currentState != newState)
            {
                previousState = currentState;
                currentState = newState;
                OnStateChanged?.Invoke(previousState, currentState);
            }
        }
        
        /// <summary>
        /// 检查状态转换是否被允许
        /// </summary>
        private bool IsTransitionAllowed(AnimationState from, AnimationState to)
        {
            if (!allowedTransitions.ContainsKey(from))
            {
                return false;
            }
            
            return allowedTransitions[from].Contains(to);
        }
        
        /// <summary>
        /// 检查状态优先级
        /// </summary>
        private bool CheckStatePriority(AnimationState newState)
        {
            if (!stateDataMap.ContainsKey(currentState) || !stateDataMap.ContainsKey(newState))
            {
                return true; // 如果没有优先级数据，允许转换
            }
            
            int currentPriority = stateDataMap[currentState].priority;
            int newPriority = stateDataMap[newState].priority;
            
            // 只有新状态优先级更高或相等时才允许转换
            return newPriority >= currentPriority;
        }
        
        /// <summary>
        /// 获取状态数据
        /// </summary>
        public AnimationStateData GetStateData(AnimationState state)
        {
            return stateDataMap.ContainsKey(state) ? stateDataMap[state] : null;
        }
        
        /// <summary>
        /// 获取当前状态数据
        /// </summary>
        public AnimationStateData GetCurrentStateData()
        {
            return GetStateData(currentState);
        }
        
        /// <summary>
        /// 重置状态机
        /// </summary>
        public void Reset()
        {
            previousState = currentState;
            currentState = AnimationState.Idle;
            OnStateChanged?.Invoke(previousState, currentState);
        }
        
        /// <summary>
        /// 检查是否可以从当前状态转换到目标状态
        /// </summary>
        public bool CanTransitionTo(AnimationState targetState)
        {
            return IsTransitionAllowed(currentState, targetState) && CheckStatePriority(targetState);
        }
        
        /// <summary>
        /// 获取允许的转换状态列表
        /// </summary>
        public List<AnimationState> GetAllowedTransitions()
        {
            return allowedTransitions.ContainsKey(currentState) ? 
                new List<AnimationState>(allowedTransitions[currentState]) : 
                new List<AnimationState>();
        }
        
        /// <summary>
        /// 添加自定义转换规则
        /// </summary>
        public void AddTransitionRule(AnimationState from, AnimationState to)
        {
            if (!allowedTransitions.ContainsKey(from))
            {
                allowedTransitions[from] = new List<AnimationState>();
            }
            
            if (!allowedTransitions[from].Contains(to))
            {
                allowedTransitions[from].Add(to);
            }
        }
        
        /// <summary>
        /// 移除转换规则
        /// </summary>
        public void RemoveTransitionRule(AnimationState from, AnimationState to)
        {
            if (allowedTransitions.ContainsKey(from))
            {
                allowedTransitions[from].Remove(to);
            }
        }
    }
}
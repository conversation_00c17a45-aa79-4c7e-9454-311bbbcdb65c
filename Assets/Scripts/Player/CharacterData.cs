using UnityEngine;

namespace MobileScrollingGame.Player
{
    /// <summary>
    /// 角色数据结构 - 存储角色的配置和状态信息
    /// </summary>
    [System.Serializable]
    public class CharacterData
    {
        [Header("移动参数")]
        public float moveSpeed = 5f;
        public float jumpForce = 10f;
        
        [Header("生命值")]
        public int maxHealth = 100;
        public int currentHealth = 100;
        
        [Header("状态")]
        public bool isGrounded = false;
        public bool facingRight = true;
        
        [Header("物理参数")]
        public float groundCheckDistance = 0.1f;
        public LayerMask groundLayerMask = 1;
        
        /// <summary>
        /// 重置角色数据到默认状态
        /// </summary>
        public void Reset()
        {
            currentHealth = maxHealth;
            isGrounded = false;
            facingRight = true;
        }
        
        /// <summary>
        /// 验证数据的有效性
        /// </summary>
        public bool IsValid()
        {
            return moveSpeed > 0 && jumpForce > 0 && maxHealth > 0 && groundCheckDistance > 0;
        }
    }
}
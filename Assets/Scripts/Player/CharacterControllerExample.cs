using UnityEngine;
using MobileScrollingGame.Player;
using MobileScrollingGame.Core;

namespace MobileScrollingGame.Player
{
    /// <summary>
    /// 角色控制器使用示例
    /// 展示如何使用CharacterController进行基础角色控制
    /// </summary>
    public class CharacterControllerExample : MonoBehaviour
    {
        [Header("角色控制器引用")]
        [SerializeField] private CharacterController characterController;
        
        [Header("输入处理器引用")]
        [SerializeField] private GameObject inputSystemObject;
        private IInputHandler inputHandler;
        
        [Header("调试信息")]
        [SerializeField] private bool showDebugInfo = true;
        
        private void Start()
        {
            // 获取角色控制器
            if (characterController == null)
            {
                characterController = GetComponent<CharacterController>();
            }
            
            // 获取输入处理器
            if (inputSystemObject != null)
            {
                inputHandler = inputSystemObject.GetComponent<IInputHandler>();
            }
            
            // 订阅角色事件
            SubscribeToCharacterEvents();
            
            // 设置示例角色数据
            SetupExampleCharacterData();
        }
        
        private void Update()
        {
            HandleInput();
            
            if (showDebugInfo)
            {
                DisplayDebugInfo();
            }
        }
        
        private void OnDestroy()
        {
            UnsubscribeFromCharacterEvents();
        }
        
        /// <summary>
        /// 处理输入
        /// </summary>
        private void HandleInput()
        {
            if (inputHandler == null || characterController == null) return;
            
            // 获取移动输入
            Vector2 moveInput = inputHandler.GetMovementInput();
            characterController.Move(moveInput);
            
            // 处理跳跃输入
            if (inputHandler.GetJumpInput())
            {
                characterController.Jump();
            }
            
            // 处理动作输入
            if (inputHandler.GetActionInput())
            {
                characterController.PerformAction();
            }
            
            // 如果没有输入，设置为空闲状态
            if (!inputHandler.HasActiveInput())
            {
                characterController.SetIdleState();
            }
        }
        
        /// <summary>
        /// 订阅角色事件
        /// </summary>
        private void SubscribeToCharacterEvents()
        {
            if (characterController == null) return;
            
            characterController.OnHealthChanged += OnHealthChanged;
            characterController.OnDeath += OnCharacterDeath;
            characterController.OnAnimationChanged += OnAnimationChanged;
        }
        
        /// <summary>
        /// 取消订阅角色事件
        /// </summary>
        private void UnsubscribeFromCharacterEvents()
        {
            if (characterController == null) return;
            
            characterController.OnHealthChanged -= OnHealthChanged;
            characterController.OnDeath -= OnCharacterDeath;
            characterController.OnAnimationChanged -= OnAnimationChanged;
        }
        
        /// <summary>
        /// 设置示例角色数据
        /// </summary>
        private void SetupExampleCharacterData()
        {
            if (characterController == null) return;
            
            CharacterData exampleData = new CharacterData
            {
                moveSpeed = 6f,
                jumpForce = 12f,
                maxHealth = 100,
                currentHealth = 100,
                groundCheckDistance = 0.15f,
                groundLayerMask = LayerMask.GetMask("Ground"),
                isGrounded = false,
                facingRight = true
            };
            
            characterController.SetCharacterData(exampleData);
        }
        
        /// <summary>
        /// 生命值改变事件处理
        /// </summary>
        private void OnHealthChanged(int newHealth)
        {
            Debug.Log($"角色生命值改变: {newHealth}");
            
            // 这里可以更新UI或触发其他效果
            if (newHealth <= 20)
            {
                Debug.LogWarning("角色生命值过低！");
            }
        }
        
        /// <summary>
        /// 角色死亡事件处理
        /// </summary>
        private void OnCharacterDeath()
        {
            Debug.Log("角色死亡！");
            
            // 这里可以处理死亡逻辑，如重生、游戏结束等
            // 示例：3秒后重生
            Invoke(nameof(RespawnCharacter), 3f);
        }
        
        /// <summary>
        /// 动画改变事件处理
        /// </summary>
        private void OnAnimationChanged(string animationName)
        {
            if (showDebugInfo)
            {
                Debug.Log($"角色动画改变: {animationName}");
            }
            
            // 这里可以处理动画相关的逻辑
            // 例如播放音效、粒子效果等
        }
        
        /// <summary>
        /// 重生角色
        /// </summary>
        private void RespawnCharacter()
        {
            // 重置角色数据
            CharacterData currentData = characterController.GetCharacterData();
            currentData.Reset();
            characterController.SetCharacterData(currentData);
            
            // 重置位置（这里使用当前位置，实际游戏中应该使用检查点位置）
            transform.position = Vector3.zero;
            
            Debug.Log("角色重生！");
        }
        
        /// <summary>
        /// 显示调试信息
        /// </summary>
        private void DisplayDebugInfo()
        {
            if (characterController == null) return;
            
            Vector3 position = characterController.GetPosition();
            int health = characterController.GetHealth();
            bool isGrounded = characterController.IsGrounded();
            
            // 在Scene视图中显示调试信息
            Debug.DrawRay(position, Vector3.up * 2f, Color.green);
            
            if (isGrounded)
            {
                Debug.DrawRay(position, Vector3.down * 0.5f, Color.green);
            }
            else
            {
                Debug.DrawRay(position, Vector3.down * 0.5f, Color.red);
            }
        }
        
        /// <summary>
        /// 公共方法：造成伤害（用于测试）
        /// </summary>
        [ContextMenu("造成伤害 (20点)")]
        public void DealDamage()
        {
            if (characterController != null)
            {
                characterController.TakeDamage(20);
            }
        }
        
        /// <summary>
        /// 公共方法：完全治愈
        /// </summary>
        [ContextMenu("完全治愈")]
        public void FullHeal()
        {
            if (characterController != null)
            {
                CharacterData data = characterController.GetCharacterData();
                data.currentHealth = data.maxHealth;
                characterController.SetCharacterData(data);
            }
        }
        
        /// <summary>
        /// 公共方法：切换调试信息显示
        /// </summary>
        [ContextMenu("切换调试信息")]
        public void ToggleDebugInfo()
        {
            showDebugInfo = !showDebugInfo;
        }
    }
}
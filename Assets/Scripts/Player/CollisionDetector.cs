using UnityEngine;
using System.Collections.Generic;

namespace MobileScrollingGame.Player
{
    /// <summary>
    /// 碰撞检测器 - 处理角色的各种碰撞检测
    /// 包括地面检测、平台碰撞、边界检测和障碍物碰撞
    /// </summary>
    [RequireComponent(typeof(Collider2D))]
    public class CollisionDetector : MonoBehaviour
    {
        [Header("地面检测设置")]
        [SerializeField] private LayerMask groundLayerMask = 1;
        [SerializeField] private float groundCheckDistance = 0.1f;
        [SerializeField] private int groundCheckRayCount = 3;
        [SerializeField] private float groundCheckWidth = 0.8f;
        
        [Header("平台检测设置")]
        [SerializeField] private LayerMask platformLayerMask = 1 << 8;
        [SerializeField] private float platformCheckDistance = 0.15f;
        
        [Header("墙壁检测设置")]
        [SerializeField] private LayerMask wallLayerMask = 1 << 9;
        [SerializeField] private float wallCheckDistance = 0.1f;
        [SerializeField] private Vector2 wallCheckOffset = new Vector2(0.4f, 0f);
        
        [Header("障碍物检测设置")]
        [SerializeField] private LayerMask obstacleLayerMask = 1 << 10;
        [SerializeField] private float obstacleCheckDistance = 0.2f;
        
        [Header("边界检测设置")]
        [SerializeField] private bool enableBoundaryCheck = true;
        [SerializeField] private Vector2 worldBounds = new Vector2(50f, 30f);
        
        [Header("调试设置")]
        [SerializeField] private bool showDebugRays = true;
        [SerializeField] private Color groundRayColor = Color.green;
        [SerializeField] private Color wallRayColor = Color.red;
        [SerializeField] private Color obstacleRayColor = Color.yellow;
        
        // 组件引用
        private Collider2D characterCollider;
        private Bounds colliderBounds;
        
        // 检测结果
        private bool isGrounded;
        private bool isOnPlatform;
        private bool isAgainstWall;
        private bool isNearObstacle;
        private bool isOutOfBounds;
        
        // 碰撞信息
        private RaycastHit2D groundHit;
        private RaycastHit2D wallHit;
        private RaycastHit2D obstacleHit;
        private List<RaycastHit2D> allGroundHits = new List<RaycastHit2D>();
        
        // 事件
        public System.Action<bool> OnGroundedChanged;
        public System.Action<bool> OnPlatformChanged;
        public System.Action<bool> OnWallCollisionChanged;
        public System.Action<bool> OnObstacleDetected;
        public System.Action<bool> OnBoundaryChanged;
        public System.Action<Collision2D> OnCollisionEnter;
        public System.Action<Collision2D> OnCollisionExit;
        
        // 属性
        public bool IsGrounded => isGrounded;
        public bool IsOnPlatform => isOnPlatform;
        public bool IsAgainstWall => isAgainstWall;
        public bool IsNearObstacle => isNearObstacle;
        public bool IsOutOfBounds => isOutOfBounds;
        public RaycastHit2D GroundHit => groundHit;
        public RaycastHit2D WallHit => wallHit;
        public RaycastHit2D ObstacleHit => obstacleHit;
        
        private void Awake()
        {
            InitializeComponents();
        }
        
        private void Update()
        {
            UpdateColliderBounds();
            PerformAllCollisionChecks();
        }
        
        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponents()
        {
            characterCollider = GetComponent<Collider2D>();
            if (characterCollider == null)
            {
                Debug.LogError($"CollisionDetector: 未找到Collider2D组件在 {gameObject.name}");
            }
        }
        
        /// <summary>
        /// 更新碰撞器边界
        /// </summary>
        private void UpdateColliderBounds()
        {
            if (characterCollider != null)
            {
                colliderBounds = characterCollider.bounds;
            }
        }
        
        /// <summary>
        /// 执行所有碰撞检测
        /// </summary>
        private void PerformAllCollisionChecks()
        {
            CheckGroundCollision();
            CheckPlatformCollision();
            CheckWallCollision();
            CheckObstacleCollision();
            CheckBoundaries();
        }
        
        /// <summary>
        /// 检测地面碰撞
        /// </summary>
        private void CheckGroundCollision()
        {
            bool wasGrounded = isGrounded;
            isGrounded = false;
            allGroundHits.Clear();
            
            if (characterCollider == null) return;
            
            // 使用多条射线检测地面，提高检测准确性
            float halfWidth = groundCheckWidth * 0.5f;
            Vector2[] checkPoints = new Vector2[groundCheckRayCount];
            
            for (int i = 0; i < groundCheckRayCount; i++)
            {
                float t = groundCheckRayCount > 1 ? (float)i / (groundCheckRayCount - 1) : 0.5f;
                float x = Mathf.Lerp(colliderBounds.center.x - halfWidth, colliderBounds.center.x + halfWidth, t);
                checkPoints[i] = new Vector2(x, colliderBounds.min.y);
            }
            
            foreach (Vector2 point in checkPoints)
            {
                RaycastHit2D hit = Physics2D.Raycast(
                    point,
                    Vector2.down,
                    groundCheckDistance,
                    groundLayerMask
                );
                
                if (hit.collider != null)
                {
                    isGrounded = true;
                    groundHit = hit;
                    allGroundHits.Add(hit);
                }
            }
            
            // 如果地面状态改变，触发事件
            if (wasGrounded != isGrounded)
            {
                OnGroundedChanged?.Invoke(isGrounded);
            }
        }
        
        /// <summary>
        /// 检测平台碰撞
        /// </summary>
        private void CheckPlatformCollision()
        {
            bool wasOnPlatform = isOnPlatform;
            
            RaycastHit2D hit = Physics2D.Raycast(
                new Vector2(colliderBounds.center.x, colliderBounds.min.y),
                Vector2.down,
                platformCheckDistance,
                platformLayerMask
            );
            
            isOnPlatform = hit.collider != null;
            
            if (wasOnPlatform != isOnPlatform)
            {
                OnPlatformChanged?.Invoke(isOnPlatform);
            }
        }
        
        /// <summary>
        /// 检测墙壁碰撞
        /// </summary>
        private void CheckWallCollision()
        {
            bool wasAgainstWall = isAgainstWall;
            isAgainstWall = false;
            
            // 检测左侧墙壁
            Vector2 leftCheckPoint = new Vector2(colliderBounds.min.x, colliderBounds.center.y);
            RaycastHit2D leftHit = Physics2D.Raycast(
                leftCheckPoint,
                Vector2.left,
                wallCheckDistance,
                wallLayerMask
            );
            
            // 检测右侧墙壁
            Vector2 rightCheckPoint = new Vector2(colliderBounds.max.x, colliderBounds.center.y);
            RaycastHit2D rightHit = Physics2D.Raycast(
                rightCheckPoint,
                Vector2.right,
                wallCheckDistance,
                wallLayerMask
            );
            
            if (leftHit.collider != null)
            {
                isAgainstWall = true;
                wallHit = leftHit;
            }
            else if (rightHit.collider != null)
            {
                isAgainstWall = true;
                wallHit = rightHit;
            }
            
            if (wasAgainstWall != isAgainstWall)
            {
                OnWallCollisionChanged?.Invoke(isAgainstWall);
            }
        }
        
        /// <summary>
        /// 检测障碍物碰撞
        /// </summary>
        private void CheckObstacleCollision()
        {
            bool wasNearObstacle = isNearObstacle;
            
            // 在角色周围进行圆形检测
            Collider2D obstacleCollider = Physics2D.OverlapCircle(
                colliderBounds.center,
                obstacleCheckDistance,
                obstacleLayerMask
            );
            
            isNearObstacle = obstacleCollider != null;
            
            if (obstacleCollider != null)
            {
                // 创建一个模拟的RaycastHit2D
                Vector2 direction = (obstacleCollider.bounds.center - colliderBounds.center).normalized;
                RaycastHit2D hit = Physics2D.Raycast(
                    colliderBounds.center,
                    direction,
                    obstacleCheckDistance,
                    obstacleLayerMask
                );
                obstacleHit = hit;
            }
            
            if (wasNearObstacle != isNearObstacle)
            {
                OnObstacleDetected?.Invoke(isNearObstacle);
            }
        }
        
        /// <summary>
        /// 检测世界边界
        /// </summary>
        private void CheckBoundaries()
        {
            if (!enableBoundaryCheck) return;
            
            bool wasOutOfBounds = isOutOfBounds;
            
            Vector3 position = transform.position;
            isOutOfBounds = position.x < -worldBounds.x || position.x > worldBounds.x ||
                           position.y < -worldBounds.y || position.y > worldBounds.y;
            
            if (wasOutOfBounds != isOutOfBounds)
            {
                OnBoundaryChanged?.Invoke(isOutOfBounds);
            }
        }
        
        /// <summary>
        /// 获取地面法线
        /// </summary>
        public Vector2 GetGroundNormal()
        {
            if (isGrounded && groundHit.collider != null)
            {
                return groundHit.normal;
            }
            return Vector2.up;
        }
        
        /// <summary>
        /// 获取地面角度
        /// </summary>
        public float GetGroundAngle()
        {
            Vector2 normal = GetGroundNormal();
            return Vector2.Angle(Vector2.up, normal);
        }
        
        /// <summary>
        /// 检查是否可以在指定方向移动
        /// </summary>
        public bool CanMoveInDirection(Vector2 direction)
        {
            if (direction.x > 0 && isAgainstWall && wallHit.normal.x < 0)
            {
                return false; // 右侧有墙壁
            }
            if (direction.x < 0 && isAgainstWall && wallHit.normal.x > 0)
            {
                return false; // 左侧有墙壁
            }
            
            return !isNearObstacle;
        }
        
        /// <summary>
        /// 获取最近的地面点
        /// </summary>
        public Vector2 GetClosestGroundPoint()
        {
            if (isGrounded && groundHit.collider != null)
            {
                return groundHit.point;
            }
            return new Vector2(transform.position.x, transform.position.y - colliderBounds.extents.y);
        }
        
        /// <summary>
        /// 设置检测参数
        /// </summary>
        public void SetDetectionParameters(float groundDistance, float wallDistance, float obstacleDistance)
        {
            groundCheckDistance = groundDistance;
            wallCheckDistance = wallDistance;
            obstacleCheckDistance = obstacleDistance;
        }
        
        /// <summary>
        /// 设置图层遮罩
        /// </summary>
        public void SetLayerMasks(LayerMask ground, LayerMask platform, LayerMask wall, LayerMask obstacle)
        {
            groundLayerMask = ground;
            platformLayerMask = platform;
            wallLayerMask = wall;
            obstacleLayerMask = obstacle;
        }
        
        /// <summary>
        /// 启用/禁用边界检测
        /// </summary>
        public void SetBoundaryCheck(bool enabled, Vector2 bounds = default)
        {
            enableBoundaryCheck = enabled;
            if (bounds != default)
            {
                worldBounds = bounds;
            }
        }
        
        /// <summary>
        /// 强制更新所有检测
        /// </summary>
        public void ForceUpdateDetection()
        {
            UpdateColliderBounds();
            PerformAllCollisionChecks();
        }
        
        #region Unity Collision Events
        
        private void OnCollisionEnter2D(Collision2D collision)
        {
            OnCollisionEnter?.Invoke(collision);
        }
        
        private void OnCollisionExit2D(Collision2D collision)
        {
            OnCollisionExit?.Invoke(collision);
        }
        
        #endregion
        
        #region Debug Visualization
        
        private void OnDrawGizmosSelected()
        {
            if (!showDebugRays || characterCollider == null) return;
            
            UpdateColliderBounds();
            
            // 绘制地面检测射线
            DrawGroundDetectionRays();
            
            // 绘制墙壁检测射线
            DrawWallDetectionRays();
            
            // 绘制障碍物检测范围
            DrawObstacleDetectionRange();
            
            // 绘制世界边界
            DrawWorldBoundaries();
        }
        
        private void DrawGroundDetectionRays()
        {
            Gizmos.color = isGrounded ? Color.green : groundRayColor;
            
            Vector2 leftPoint = new Vector2(colliderBounds.min.x + 0.1f, colliderBounds.min.y);
            Vector2 rightPoint = new Vector2(colliderBounds.max.x - 0.1f, colliderBounds.min.y);
            Vector2 centerPoint = new Vector2(colliderBounds.center.x, colliderBounds.min.y);
            
            Vector2[] checkPoints = { leftPoint, centerPoint, rightPoint };
            
            foreach (Vector2 point in checkPoints)
            {
                Gizmos.DrawLine(point, point + Vector2.down * groundCheckDistance);
            }
        }
        
        private void DrawWallDetectionRays()
        {
            Gizmos.color = isAgainstWall ? Color.red : wallRayColor;
            
            Vector2 leftCheckPoint = new Vector2(colliderBounds.min.x, colliderBounds.center.y);
            Vector2 rightCheckPoint = new Vector2(colliderBounds.max.x, colliderBounds.center.y);
            
            Gizmos.DrawLine(leftCheckPoint, leftCheckPoint + Vector2.left * wallCheckDistance);
            Gizmos.DrawLine(rightCheckPoint, rightCheckPoint + Vector2.right * wallCheckDistance);
        }
        
        private void DrawObstacleDetectionRange()
        {
            Gizmos.color = isNearObstacle ? Color.red : obstacleRayColor;
            Gizmos.DrawWireSphere(colliderBounds.center, obstacleCheckDistance);
        }
        
        private void DrawWorldBoundaries()
        {
            if (!enableBoundaryCheck) return;
            
            Gizmos.color = isOutOfBounds ? Color.red : Color.cyan;
            Vector3 center = Vector3.zero;
            Vector3 size = new Vector3(worldBounds.x * 2, worldBounds.y * 2, 0);
            Gizmos.DrawWireCube(center, size);
        }
        
        #endregion
    }
}
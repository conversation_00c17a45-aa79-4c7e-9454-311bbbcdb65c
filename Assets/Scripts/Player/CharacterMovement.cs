using UnityEngine;

namespace MobileScrollingGame.Player
{
    /// <summary>
    /// 角色移动组件 - 专门处理角色的物理移动逻辑
    /// </summary>
    [RequireComponent(typeof(Rigidbody2D))]
    public class CharacterMovement : MonoBehaviour
    {
        [SerializeField] private CharacterData characterData;
        
        // 组件引用
        private Rigidbody2D rb2d;
        private Transform groundCheckPoint;
        
        // 状态
        private Vector2 moveInput;
        private bool isGrounded;
        private bool facingRight = true;
        
        // 事件
        public System.Action<bool> OnGroundedChanged;
        public System.Action<bool> OnFacingDirectionChanged;
        
        public bool IsGrounded => isGrounded;
        public bool FacingRight => facingRight;
        public Vector2 Velocity => rb2d.linearVelocity;
        
        private void Awake()
        {
            rb2d = GetComponent<Rigidbody2D>();
            InitializeRigidbody();
            
            if (characterData == null)
            {
                characterData = new CharacterData();
            }
        }
        
        private void Start()
        {
            CreateGroundCheckPoint();
        }
        
        private void Update()
        {
            CheckGroundStatus();
        }
        
        private void FixedUpdate()
        {
            ApplyMovement();
        }
        
        /// <summary>
        /// 初始化Rigidbody2D设置
        /// </summary>
        private void InitializeRigidbody()
        {
            rb2d.freezeRotation = true;
            rb2d.collisionDetectionMode = CollisionDetectionMode2D.Continuous;
        }
        
        /// <summary>
        /// 创建地面检测点
        /// </summary>
        private void CreateGroundCheckPoint()
        {
            GameObject groundCheck = new GameObject("GroundCheckPoint");
            groundCheck.transform.SetParent(transform);
            
            Collider2D collider = GetComponent<Collider2D>();
            if (collider != null)
            {
                groundCheck.transform.localPosition = new Vector3(0, -collider.bounds.extents.y, 0);
            }
            else
            {
                groundCheck.transform.localPosition = new Vector3(0, -0.5f, 0);
            }
            
            groundCheckPoint = groundCheck.transform;
        }
        
        /// <summary>
        /// 检查地面状态
        /// </summary>
        private void CheckGroundStatus()
        {
            if (groundCheckPoint == null) return;
            
            bool wasGrounded = isGrounded;
            
            // 首先尝试使用CollisionDetector（如果存在）
            CollisionDetector collisionDetector = GetComponent<CollisionDetector>();
            if (collisionDetector != null)
            {
                isGrounded = collisionDetector.IsGrounded;
            }
            else
            {
                // 回退到原始的射线投射检测
                RaycastHit2D hit = Physics2D.Raycast(
                    groundCheckPoint.position,
                    Vector2.down,
                    characterData.groundCheckDistance,
                    characterData.groundLayerMask
                );
                
                isGrounded = hit.collider != null;
            }
            
            // 如果地面状态改变，触发事件
            if (wasGrounded != isGrounded)
            {
                OnGroundedChanged?.Invoke(isGrounded);
            }
        }
        
        /// <summary>
        /// 应用移动
        /// </summary>
        private void ApplyMovement()
        {
            // 水平移动
            Vector2 velocity = rb2d.linearVelocity;
            velocity.x = moveInput.x * characterData.moveSpeed;
            rb2d.linearVelocity = velocity;
            
            // 处理角色翻转
            HandleFacing();
        }
        
        /// <summary>
        /// 处理角色朝向
        /// </summary>
        private void HandleFacing()
        {
            bool shouldFaceRight = moveInput.x > 0;
            bool shouldFaceLeft = moveInput.x < 0;
            
            if (shouldFaceRight && !facingRight)
            {
                Flip();
            }
            else if (shouldFaceLeft && facingRight)
            {
                Flip();
            }
        }
        
        /// <summary>
        /// 翻转角色
        /// </summary>
        private void Flip()
        {
            facingRight = !facingRight;
            Vector3 scale = transform.localScale;
            scale.x *= -1;
            transform.localScale = scale;
            
            OnFacingDirectionChanged?.Invoke(facingRight);
        }
        
        /// <summary>
        /// 设置移动输入
        /// </summary>
        public void SetMoveInput(Vector2 input)
        {
            moveInput = input;
        }
        
        /// <summary>
        /// 执行跳跃
        /// </summary>
        public bool TryJump()
        {
            if (isGrounded)
            {
                rb2d.linearVelocity = new Vector2(rb2d.linearVelocity.x, characterData.jumpForce);
                return true;
            }
            return false;
        }
        
        /// <summary>
        /// 停止移动
        /// </summary>
        public void StopMovement()
        {
            moveInput = Vector2.zero;
        }
        
        /// <summary>
        /// 设置角色数据
        /// </summary>
        public void SetCharacterData(CharacterData data)
        {
            characterData = data;
        }
        
        /// <summary>
        /// 获取角色数据
        /// </summary>
        public CharacterData GetCharacterData()
        {
            return characterData;
        }
        
        private void OnDrawGizmosSelected()
        {
            if (groundCheckPoint != null && characterData != null)
            {
                Gizmos.color = isGrounded ? Color.green : Color.red;
                Gizmos.DrawLine(
                    groundCheckPoint.position,
                    groundCheckPoint.position + Vector3.down * characterData.groundCheckDistance
                );
            }
        }
    }
}
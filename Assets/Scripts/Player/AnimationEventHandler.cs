using UnityEngine;

namespace MobileScrollingGame.Player
{
    /// <summary>
    /// 动画事件处理器 - 处理来自Animator的动画事件
    /// 用于在动画的特定帧触发代码逻辑
    /// </summary>
    public class AnimationEventHandler : MonoBehaviour
    {
        [Header("组件引用")]
        [SerializeField] private CharacterController characterController;
        [SerializeField] private CharacterAnimator characterAnimator;
        
        [Header("调试")]
        [SerializeField] private bool debugMode = false;
        
        // 事件
        public System.Action<string> OnAnimationEvent;
        public System.Action OnFootstep;
        public System.Action OnLandingImpact;
        public System.Action OnActionHit;
        public System.Action OnAnimationComplete;
        
        private void Awake()
        {
            // 自动获取组件引用
            if (characterController == null)
            {
                characterController = GetComponent<CharacterController>();
            }
            
            if (characterAnimator == null)
            {
                characterAnimator = GetComponent<CharacterAnimator>();
            }
        }
        
        #region Animation Events (Called by Animator)
        
        /// <summary>
        /// 脚步声事件 - 在行走动画中调用
        /// </summary>
        public void OnFootstepEvent()
        {
            if (debugMode)
            {
                Debug.Log("AnimationEventHandler: 脚步声事件");
            }
            
            OnFootstep?.Invoke();
            OnAnimationEvent?.Invoke("Footstep");
        }
        
        /// <summary>
        /// 着陆冲击事件 - 在着陆动画中调用
        /// </summary>
        public void OnLandingImpactEvent()
        {
            if (debugMode)
            {
                Debug.Log("AnimationEventHandler: 着陆冲击事件");
            }
            
            OnLandingImpact?.Invoke();
            OnAnimationEvent?.Invoke("LandingImpact");
        }
        
        /// <summary>
        /// 动作命中事件 - 在动作动画中调用
        /// </summary>
        public void OnActionHitEvent()
        {
            if (debugMode)
            {
                Debug.Log("AnimationEventHandler: 动作命中事件");
            }
            
            OnActionHit?.Invoke();
            OnAnimationEvent?.Invoke("ActionHit");
        }
        
        /// <summary>
        /// 跳跃开始事件 - 在跳跃动画开始时调用
        /// </summary>
        public void OnJumpStartEvent()
        {
            if (debugMode)
            {
                Debug.Log("AnimationEventHandler: 跳跃开始事件");
            }
            
            OnAnimationEvent?.Invoke("JumpStart");
        }
        
        /// <summary>
        /// 着陆完成事件 - 在着陆动画完成时调用
        /// </summary>
        public void OnLandingCompleteEvent()
        {
            if (debugMode)
            {
                Debug.Log("AnimationEventHandler: 着陆完成事件");
            }
            
            // 通知动画器着陆完成
            if (characterAnimator != null)
            {
                characterAnimator.OnLandingComplete();
            }
            
            OnAnimationComplete?.Invoke();
            OnAnimationEvent?.Invoke("LandingComplete");
        }
        
        /// <summary>
        /// 动作完成事件 - 在动作动画完成时调用
        /// </summary>
        public void OnActionCompleteEvent()
        {
            if (debugMode)
            {
                Debug.Log("AnimationEventHandler: 动作完成事件");
            }
            
            // 通知动画器动作完成
            if (characterAnimator != null)
            {
                characterAnimator.OnActionComplete();
            }
            
            OnAnimationComplete?.Invoke();
            OnAnimationEvent?.Invoke("ActionComplete");
        }
        
        /// <summary>
        /// 通用动画事件 - 可以传递参数
        /// </summary>
        public void OnGenericAnimationEvent(string eventName)
        {
            if (debugMode)
            {
                Debug.Log($"AnimationEventHandler: 通用动画事件 - {eventName}");
            }
            
            OnAnimationEvent?.Invoke(eventName);
            
            // 根据事件名称执行特定逻辑
            HandleGenericEvent(eventName);
        }
        
        /// <summary>
        /// 处理通用事件
        /// </summary>
        private void HandleGenericEvent(string eventName)
        {
            switch (eventName.ToLower())
            {
                case "footstep":
                    OnFootstep?.Invoke();
                    break;
                case "landingimpact":
                    OnLandingImpact?.Invoke();
                    break;
                case "actionhit":
                    OnActionHit?.Invoke();
                    break;
                case "animationcomplete":
                    OnAnimationComplete?.Invoke();
                    break;
            }
        }
        
        #endregion
        
        #region Public Methods
        
        /// <summary>
        /// 设置调试模式
        /// </summary>
        public void SetDebugMode(bool enabled)
        {
            debugMode = enabled;
        }
        
        /// <summary>
        /// 设置角色控制器引用
        /// </summary>
        public void SetCharacterController(CharacterController controller)
        {
            characterController = controller;
        }
        
        /// <summary>
        /// 设置角色动画器引用
        /// </summary>
        public void SetCharacterAnimator(CharacterAnimator animator)
        {
            characterAnimator = animator;
        }
        
        /// <summary>
        /// 手动触发动画事件（用于测试）
        /// </summary>
        public void TriggerAnimationEvent(string eventName)
        {
            OnGenericAnimationEvent(eventName);
        }
        
        #endregion
        
        #region Event Subscription Helpers
        
        /// <summary>
        /// 订阅脚步声事件
        /// </summary>
        public void SubscribeToFootstep(System.Action callback)
        {
            OnFootstep += callback;
        }
        
        /// <summary>
        /// 取消订阅脚步声事件
        /// </summary>
        public void UnsubscribeFromFootstep(System.Action callback)
        {
            OnFootstep -= callback;
        }
        
        /// <summary>
        /// 订阅着陆冲击事件
        /// </summary>
        public void SubscribeToLandingImpact(System.Action callback)
        {
            OnLandingImpact += callback;
        }
        
        /// <summary>
        /// 取消订阅着陆冲击事件
        /// </summary>
        public void UnsubscribeFromLandingImpact(System.Action callback)
        {
            OnLandingImpact -= callback;
        }
        
        /// <summary>
        /// 订阅动作命中事件
        /// </summary>
        public void SubscribeToActionHit(System.Action callback)
        {
            OnActionHit += callback;
        }
        
        /// <summary>
        /// 取消订阅动作命中事件
        /// </summary>
        public void UnsubscribeFromActionHit(System.Action callback)
        {
            OnActionHit -= callback;
        }
        
        /// <summary>
        /// 订阅动画完成事件
        /// </summary>
        public void SubscribeToAnimationComplete(System.Action callback)
        {
            OnAnimationComplete += callback;
        }
        
        /// <summary>
        /// 取消订阅动画完成事件
        /// </summary>
        public void UnsubscribeFromAnimationComplete(System.Action callback)
        {
            OnAnimationComplete -= callback;
        }
        
        /// <summary>
        /// 订阅通用动画事件
        /// </summary>
        public void SubscribeToAnimationEvent(System.Action<string> callback)
        {
            OnAnimationEvent += callback;
        }
        
        /// <summary>
        /// 取消订阅通用动画事件
        /// </summary>
        public void UnsubscribeFromAnimationEvent(System.Action<string> callback)
        {
            OnAnimationEvent -= callback;
        }
        
        #endregion
    }
}
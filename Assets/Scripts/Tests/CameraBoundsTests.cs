using NUnit.Framework;
using UnityEngine;
using MobileScrollingGame.Camera;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// CameraBounds组件的单元测试
    /// 测试边界设置、碰撞器集成和边界检查功能
    /// </summary>
    public class CameraBoundsTests
    {
        private GameObject boundsObject;
        private CameraBounds cameraBounds;
        private BoxCollider2D boundsCollider;
        
        [SetUp]
        public void SetUp()
        {
            // 创建边界对象
            boundsObject = new GameObject("TestBounds");
            cameraBounds = boundsObject.AddComponent<CameraBounds>();
            boundsCollider = boundsObject.AddComponent<BoxCollider2D>();
        }
        
        [TearDown]
        public void TearDown()
        {
            if (boundsObject != null)
                Object.DestroyImmediate(boundsObject);
        }
        
        [Test]
        public void SetBounds_UpdatesBoundsCorrectly()
        {
            // Arrange
            Rect testBounds = new Rect(-5, -3, 10, 6);
            
            // Act
            cameraBounds.SetBounds(testBounds);
            
            // Assert
            Rect actualBounds = cameraBounds.GetBounds();
            Assert.AreEqual(testBounds, actualBounds);
        }
        
        [Test]
        public void SetBounds_WithCenterAndSize_CalculatesCorrectBounds()
        {
            // Arrange
            Vector2 center = new Vector2(2, 1);
            Vector2 size = new Vector2(8, 4);
            
            // Act
            cameraBounds.SetBounds(center, size);
            
            // Assert
            Rect actualBounds = cameraBounds.GetBounds();
            Assert.AreEqual(-2f, actualBounds.x, 0.01f); // center.x - size.x/2
            Assert.AreEqual(-1f, actualBounds.y, 0.01f); // center.y - size.y/2
            Assert.AreEqual(8f, actualBounds.width, 0.01f);
            Assert.AreEqual(4f, actualBounds.height, 0.01f);
        }
        
        [Test]
        public void ExpandBounds_IncreasesAllSides()
        {
            // Arrange
            Rect originalBounds = new Rect(-2, -1, 4, 2);
            cameraBounds.SetBounds(originalBounds);
            float expansion = 1f;
            
            // Act
            cameraBounds.ExpandBounds(expansion);
            
            // Assert
            Rect expandedBounds = cameraBounds.GetBounds();
            Assert.AreEqual(originalBounds.x - expansion, expandedBounds.x, 0.01f);
            Assert.AreEqual(originalBounds.y - expansion, expandedBounds.y, 0.01f);
            Assert.AreEqual(originalBounds.width + expansion * 2f, expandedBounds.width, 0.01f);
            Assert.AreEqual(originalBounds.height + expansion * 2f, expandedBounds.height, 0.01f);
        }
        
        [Test]
        public void IsPointInBounds_ReturnsTrueForPointInside()
        {
            // Arrange
            Rect bounds = new Rect(-5, -3, 10, 6);
            cameraBounds.SetBounds(bounds);
            Vector2 pointInside = new Vector2(2, 1);
            
            // Act
            bool result = cameraBounds.IsPointInBounds(pointInside);
            
            // Assert
            Assert.IsTrue(result, "边界内的点应该返回true");
        }
        
        [Test]
        public void IsPointInBounds_ReturnsFalseForPointOutside()
        {
            // Arrange
            Rect bounds = new Rect(-5, -3, 10, 6);
            cameraBounds.SetBounds(bounds);
            Vector2 pointOutside = new Vector2(10, 1);
            
            // Act
            bool result = cameraBounds.IsPointInBounds(pointOutside);
            
            // Assert
            Assert.IsFalse(result, "边界外的点应该返回false");
        }
        
        [Test]
        public void ClampPointToBounds_ClampsPointCorrectly()
        {
            // Arrange
            Rect bounds = new Rect(-5, -3, 10, 6);
            cameraBounds.SetBounds(bounds);
            Vector2 pointOutside = new Vector2(10, 5);
            
            // Act
            Vector2 clampedPoint = cameraBounds.ClampPointToBounds(pointOutside);
            
            // Assert
            Assert.AreEqual(bounds.xMax, clampedPoint.x, 0.01f, "X坐标应该被限制到右边界");
            Assert.AreEqual(bounds.yMax, clampedPoint.y, 0.01f, "Y坐标应该被限制到上边界");
        }
        
        [Test]
        public void ClampPointToBounds_DoesNotChangePointInside()
        {
            // Arrange
            Rect bounds = new Rect(-5, -3, 10, 6);
            cameraBounds.SetBounds(bounds);
            Vector2 pointInside = new Vector2(2, 1);
            
            // Act
            Vector2 clampedPoint = cameraBounds.ClampPointToBounds(pointInside);
            
            // Assert
            Assert.AreEqual(pointInside, clampedPoint, "边界内的点不应该被改变");
        }
        
        [Test]
        public void GetBounds_ReturnsCurrentBounds()
        {
            // Arrange
            Rect testBounds = new Rect(-3, -2, 6, 4);
            cameraBounds.SetBounds(testBounds);
            
            // Act
            Rect returnedBounds = cameraBounds.GetBounds();
            
            // Assert
            Assert.AreEqual(testBounds, returnedBounds);
        }
        
        [Test]
        public void BoundsChangedEvent_TriggersWhenBoundsSet()
        {
            // Arrange
            bool eventTriggered = false;
            Rect eventBounds = default;
            cameraBounds.OnBoundsChanged += (bounds) => 
            {
                eventTriggered = true;
                eventBounds = bounds;
            };
            
            Rect newBounds = new Rect(-4, -2, 8, 4);
            
            // Act
            cameraBounds.SetBounds(newBounds);
            
            // Assert
            Assert.IsTrue(eventTriggered, "边界改变事件应该被触发");
            Assert.AreEqual(newBounds, eventBounds, "事件应该传递正确的边界数据");
        }
        
        [Test]
        public void BoundsChangedEvent_TriggersWhenBoundsExpanded()
        {
            // Arrange
            bool eventTriggered = false;
            cameraBounds.SetBounds(new Rect(-2, -1, 4, 2));
            cameraBounds.OnBoundsChanged += (bounds) => { eventTriggered = true; };
            
            // Act
            cameraBounds.ExpandBounds(1f);
            
            // Assert
            Assert.IsTrue(eventTriggered, "扩展边界时应该触发事件");
        }
        
        [Test]
        public void ClampPointToBounds_HandlesEdgeCases()
        {
            // Arrange
            Rect bounds = new Rect(-1, -1, 2, 2);
            cameraBounds.SetBounds(bounds);
            
            // Test corner points
            Vector2[] testPoints = {
                new Vector2(-2, -2), // 左下角外
                new Vector2(2, 2),   // 右上角外
                new Vector2(-2, 2),  // 左上角外
                new Vector2(2, -2)   // 右下角外
            };
            
            Vector2[] expectedPoints = {
                new Vector2(-1, -1), // 应该被限制到左下角
                new Vector2(1, 1),   // 应该被限制到右上角
                new Vector2(-1, 1),  // 应该被限制到左上角
                new Vector2(1, -1)   // 应该被限制到右下角
            };
            
            // Act & Assert
            for (int i = 0; i < testPoints.Length; i++)
            {
                Vector2 clampedPoint = cameraBounds.ClampPointToBounds(testPoints[i]);
                Assert.AreEqual(expectedPoints[i], clampedPoint, $"测试点 {i} 限制失败");
            }
        }
    }
}
using UnityEngine;
using UnityEngine.TestTools;
using NUnit.Framework;
using System.Collections;
using MobileScrollingGame.Player;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 平台碰撞处理器测试类
    /// 测试平台检测、单向平台、移动平台和穿透平台功能
    /// </summary>
    public class PlatformCollisionHandlerTests
    {
        private GameObject testCharacter;
        private PlatformCollisionHandler platformHandler;
        private CollisionDetector collisionDetector;
        private BoxCollider2D characterCollider;
        private Rigidbody2D characterRigidbody;
        
        // 测试平台对象
        private GameObject normalPlatform;
        private GameObject oneWayPlatform;
        private GameObject movingPlatform;
        
        [SetUp]
        public void SetUp()
        {
            // 创建测试角色
            testCharacter = new GameObject("TestCharacter");
            characterCollider = testCharacter.AddComponent<BoxCollider2D>();
            characterRigidbody = testCharacter.AddComponent<Rigidbody2D>();
            collisionDetector = testCharacter.AddComponent<CollisionDetector>();
            platformHandler = testCharacter.AddComponent<PlatformCollisionHandler>();
            
            // 设置角色属性
            characterCollider.size = new Vector2(1f, 2f);
            characterRigidbody.gravityScale = 1f;
            characterRigidbody.freezeRotation = true;
            
            // 创建测试平台
            CreateTestPlatforms();
            
            // 设置平台处理器参数
            SetupPlatformHandler();
        }
        
        [TearDown]
        public void TearDown()
        {
            // 清理测试对象
            if (testCharacter != null)
                Object.DestroyImmediate(testCharacter);
            if (normalPlatform != null)
                Object.DestroyImmediate(normalPlatform);
            if (oneWayPlatform != null)
                Object.DestroyImmediate(oneWayPlatform);
            if (movingPlatform != null)
                Object.DestroyImmediate(movingPlatform);
        }
        
        /// <summary>
        /// 创建测试平台
        /// </summary>
        private void CreateTestPlatforms()
        {
            // 创建普通平台
            normalPlatform = new GameObject("NormalPlatform");
            normalPlatform.layer = 8; // Platform layer
            var normalCollider = normalPlatform.AddComponent<BoxCollider2D>();
            normalCollider.size = new Vector2(4f, 0.5f);
            normalPlatform.transform.position = new Vector3(0f, -1f, 0f);
            
            // 创建单向平台
            oneWayPlatform = new GameObject("OneWayPlatform");
            oneWayPlatform.layer = 11; // OneWay platform layer
            var oneWayCollider = oneWayPlatform.AddComponent<BoxCollider2D>();
            oneWayCollider.size = new Vector2(4f, 0.5f);
            oneWayPlatform.transform.position = new Vector3(6f, 1f, 0f);
            
            // 创建移动平台
            movingPlatform = new GameObject("MovingPlatform");
            movingPlatform.layer = 8; // Platform layer
            var movingCollider = movingPlatform.AddComponent<BoxCollider2D>();
            movingCollider.size = new Vector2(3f, 0.5f);
            movingPlatform.transform.position = new Vector3(-6f, 2f, 0f);
            
            // 为移动平台添加简单的移动脚本
            var movingScript = movingPlatform.AddComponent<TestMovingPlatform>();
            movingScript.Initialize();
        }
        
        /// <summary>
        /// 设置平台处理器参数
        /// </summary>
        private void SetupPlatformHandler()
        {
            LayerMask platformMask = 1 << 8; // Platform layer
            LayerMask oneWayMask = 1 << 11; // OneWay platform layer
            
            platformHandler.SetPlatformLayerMasks(platformMask, oneWayMask);
            platformHandler.SetDropThroughSettings(true, KeyCode.S, 0.5f);
            platformHandler.SetMovingPlatformSupport(true, 1f);
        }
        
        #region 普通平台检测测试
        
        [UnityTest]
        public IEnumerator NormalPlatform_WhenCharacterOnPlatform_ShouldDetectPlatform()
        {
            // Arrange
            testCharacter.transform.position = new Vector3(0f, -0.2f, 0f);
            
            // Act
            yield return new WaitForFixedUpdate();
            
            // Assert
            Assert.IsTrue(platformHandler.IsOnPlatform, "角色在普通平台上时应该检测到平台");
            Assert.IsNotNull(platformHandler.CurrentPlatform, "应该有当前平台引用");
            Assert.AreEqual(normalPlatform.GetComponent<Collider2D>(), platformHandler.CurrentPlatform, "当前平台应该是正确的平台");
        }
        
        [UnityTest]
        public IEnumerator NormalPlatform_WhenCharacterNotOnPlatform_ShouldNotDetectPlatform()
        {
            // Arrange
            testCharacter.transform.position = new Vector3(10f, -0.2f, 0f); // 远离平台
            
            // Act
            yield return new WaitForFixedUpdate();
            
            // Assert
            Assert.IsFalse(platformHandler.IsOnPlatform, "角色不在平台上时不应该检测到平台");
            Assert.IsNull(platformHandler.CurrentPlatform, "不应该有当前平台引用");
        }
        
        #endregion
        
        #region 单向平台测试
        
        [UnityTest]
        public IEnumerator OneWayPlatform_WhenCharacterLandsFromAbove_ShouldDetectPlatform()
        {
            // Arrange
            testCharacter.transform.position = new Vector3(6f, 3f, 0f); // 在单向平台上方
            characterRigidbody.linearVelocity = Vector2.down * 2f; // 向下移动
            
            // Act - 等待角色落到平台上
            yield return new WaitForSeconds(0.5f);
            
            // Assert
            Assert.IsTrue(platformHandler.IsOnPlatform, "角色从上方落到单向平台时应该检测到平台");
            Assert.IsTrue(platformHandler.IsOnOneWayPlatform, "应该检测到这是单向平台");
        }
        
        [UnityTest]
        public IEnumerator OneWayPlatform_WhenCharacterApproachesFromBelow_ShouldNotDetectPlatform()
        {
            // Arrange
            testCharacter.transform.position = new Vector3(6f, 0f, 0f); // 在单向平台下方
            characterRigidbody.linearVelocity = Vector2.up * 2f; // 向上移动
            
            // Act
            yield return new WaitForSeconds(0.5f);
            
            // Assert
            Assert.IsFalse(platformHandler.IsOnPlatform, "角色从下方接触单向平台时不应该检测到平台");
        }
        
        [UnityTest]
        public IEnumerator OneWayPlatform_DropThrough_ShouldIgnorePlatform()
        {
            // Arrange - 角色在单向平台上
            testCharacter.transform.position = new Vector3(6f, 1.8f, 0f);
            yield return new WaitForFixedUpdate();
            
            Assert.IsTrue(platformHandler.IsOnOneWayPlatform, "前置条件：角色应该在单向平台上");
            
            // Act - 触发穿透
            platformHandler.TriggerDropThrough();
            yield return new WaitForSeconds(0.1f);
            
            // Assert
            Assert.IsTrue(platformHandler.IsDroppingThrough, "应该正在穿透平台");
            Assert.IsFalse(platformHandler.IsOnPlatform, "穿透时不应该检测到平台");
        }
        
        [UnityTest]
        public IEnumerator OneWayPlatform_DropThroughTimeout_ShouldReEnablePlatform()
        {
            // Arrange - 角色在单向平台上并开始穿透
            testCharacter.transform.position = new Vector3(6f, 1.8f, 0f);
            yield return new WaitForFixedUpdate();
            platformHandler.TriggerDropThrough();
            
            // Act - 等待穿透超时
            yield return new WaitForSeconds(0.6f); // 超过0.5秒的穿透时间
            
            // Assert
            Assert.IsFalse(platformHandler.IsDroppingThrough, "穿透超时后应该停止穿透");
        }
        
        #endregion
        
        #region 移动平台测试
        
        [UnityTest]
        public IEnumerator MovingPlatform_WhenCharacterOnMovingPlatform_ShouldTrackPlatformMovement()
        {
            // Arrange
            testCharacter.transform.position = new Vector3(-6f, 2.8f, 0f); // 在移动平台上
            yield return new WaitForFixedUpdate();
            
            Assert.IsTrue(platformHandler.IsOnPlatform, "前置条件：角色应该在移动平台上");
            
            // Act - 等待平台移动
            Vector3 initialPosition = testCharacter.transform.position;
            yield return new WaitForSeconds(0.5f);
            Vector3 finalPosition = testCharacter.transform.position;
            
            // Assert
            Assert.AreNotEqual(initialPosition.x, finalPosition.x, "角色应该随移动平台一起移动");
            Assert.AreNotEqual(Vector3.zero, platformHandler.PlatformVelocity, "应该检测到平台速度");
        }
        
        [UnityTest]
        public IEnumerator MovingPlatform_WhenCharacterLeavesMovingPlatform_ShouldStopTrackingMovement()
        {
            // Arrange - 角色在移动平台上
            testCharacter.transform.position = new Vector3(-6f, 2.8f, 0f);
            yield return new WaitForFixedUpdate();
            
            // Act - 将角色移离平台
            testCharacter.transform.position = new Vector3(0f, 0f, 0f);
            yield return new WaitForFixedUpdate();
            
            // Assert
            Assert.IsFalse(platformHandler.IsOnPlatform, "角色离开平台后不应该检测到平台");
            Assert.AreEqual(Vector3.zero, platformHandler.PlatformVelocity, "离开平台后速度应该为零");
        }
        
        #endregion
        
        #region 平台切换测试
        
        [UnityTest]
        public IEnumerator PlatformSwitching_WhenMovingBetweenPlatforms_ShouldTriggerCorrectEvents()
        {
            // Arrange
            bool enterEventTriggered = false;
            bool exitEventTriggered = false;
            Collider2D enteredPlatform = null;
            Collider2D exitedPlatform = null;
            
            platformHandler.OnPlatformEnter += (platform) =>
            {
                enterEventTriggered = true;
                enteredPlatform = platform;
            };
            
            platformHandler.OnPlatformExit += (platform) =>
            {
                exitEventTriggered = true;
                exitedPlatform = platform;
            };
            
            // Act - 移动到第一个平台
            testCharacter.transform.position = new Vector3(0f, -0.2f, 0f);
            yield return new WaitForFixedUpdate();
            
            Assert.IsTrue(enterEventTriggered, "进入平台时应该触发进入事件");
            Assert.AreEqual(normalPlatform.GetComponent<Collider2D>(), enteredPlatform, "进入事件应该传递正确的平台");
            
            // 重置事件标志
            enterEventTriggered = false;
            exitEventTriggered = false;
            
            // Act - 移动到第二个平台
            testCharacter.transform.position = new Vector3(6f, 1.8f, 0f);
            yield return new WaitForFixedUpdate();
            
            // Assert
            Assert.IsTrue(exitEventTriggered, "离开平台时应该触发离开事件");
            Assert.IsTrue(enterEventTriggered, "进入新平台时应该触发进入事件");
            Assert.AreEqual(normalPlatform.GetComponent<Collider2D>(), exitedPlatform, "离开事件应该传递正确的平台");
            Assert.AreEqual(oneWayPlatform.GetComponent<Collider2D>(), enteredPlatform, "进入事件应该传递正确的新平台");
        }
        
        #endregion
        
        #region 配置测试
        
        [Test]
        public void SetPlatformLayerMasks_ShouldUpdateDetectionLayers()
        {
            // Arrange
            LayerMask newPlatformMask = 1 << 15; // 使用不同的图层
            LayerMask newOneWayMask = 1 << 16;
            
            // Act
            platformHandler.SetPlatformLayerMasks(newPlatformMask, newOneWayMask);
            testCharacter.transform.position = new Vector3(0f, -0.2f, 0f);
            
            // Assert
            // 由于平台在不同图层，不应该检测到
            Assert.IsFalse(platformHandler.IsOnPlatform, "更新图层遮罩后不应该检测到不同图层的平台");
        }
        
        [Test]
        public void SetDropThroughSettings_ShouldUpdateDropThroughBehavior()
        {
            // Arrange & Act
            platformHandler.SetDropThroughSettings(false, KeyCode.Space, 1f);
            testCharacter.transform.position = new Vector3(6f, 1.8f, 0f);
            
            // 尝试触发穿透
            platformHandler.TriggerDropThrough();
            
            // Assert
            Assert.IsFalse(platformHandler.IsDroppingThrough, "禁用穿透功能后不应该能穿透平台");
        }
        
        [Test]
        public void SetMovingPlatformSupport_ShouldUpdateMovingPlatformBehavior()
        {
            // Arrange & Act
            platformHandler.SetMovingPlatformSupport(false, 0f);
            
            // 这个测试需要在运行时验证，因为移动平台影响是在FixedUpdate中应用的
            // 这里主要测试设置是否被正确接受
            Assert.IsTrue(true, "设置移动平台支持应该成功");
        }
        
        #endregion
        
        #region 边界情况测试
        
        [UnityTest]
        public IEnumerator EdgeCase_MultipleDropThroughAttempts_ShouldHandleCorrectly()
        {
            // Arrange - 角色在单向平台上
            testCharacter.transform.position = new Vector3(6f, 1.8f, 0f);
            yield return new WaitForFixedUpdate();
            
            // Act - 多次尝试穿透
            platformHandler.TriggerDropThrough();
            platformHandler.TriggerDropThrough(); // 第二次尝试
            platformHandler.TriggerDropThrough(); // 第三次尝试
            
            // Assert
            Assert.IsTrue(platformHandler.IsDroppingThrough, "多次穿透尝试应该只触发一次");
        }
        
        [UnityTest]
        public IEnumerator EdgeCase_ForceLeavePlatform_ShouldResetState()
        {
            // Arrange - 角色在平台上
            testCharacter.transform.position = new Vector3(0f, -0.2f, 0f);
            yield return new WaitForFixedUpdate();
            
            Assert.IsTrue(platformHandler.IsOnPlatform, "前置条件：角色应该在平台上");
            
            // Act
            platformHandler.ForceLeavePlatform();
            
            // Assert
            Assert.IsFalse(platformHandler.IsOnPlatform, "强制离开平台后不应该检测到平台");
            Assert.IsNull(platformHandler.CurrentPlatform, "当前平台引用应该被清除");
        }
        
        [Test]
        public void EdgeCase_ClearIgnoredPlatforms_ShouldResetIgnoreList()
        {
            // Arrange - 触发穿透以添加忽略的平台
            testCharacter.transform.position = new Vector3(6f, 1.8f, 0f);
            platformHandler.TriggerDropThrough();
            
            // Act
            platformHandler.ClearIgnoredPlatforms();
            
            // Assert
            Assert.IsFalse(platformHandler.IsDroppingThrough, "清除忽略平台后应该停止穿透状态");
        }
        
        #endregion
    }
    
    /// <summary>
    /// 测试用的简单移动平台脚本
    /// </summary>
    public class TestMovingPlatform : MonoBehaviour
    {
        private Vector3 startPosition;
        private float moveSpeed = 2f;
        private float moveRange = 2f;
        private bool movingRight = true;
        
        public void Initialize()
        {
            startPosition = transform.position;
        }
        
        private void Update()
        {
            // 简单的左右移动
            if (movingRight)
            {
                transform.position += Vector3.right * moveSpeed * Time.deltaTime;
                if (transform.position.x >= startPosition.x + moveRange)
                {
                    movingRight = false;
                }
            }
            else
            {
                transform.position += Vector3.left * moveSpeed * Time.deltaTime;
                if (transform.position.x <= startPosition.x - moveRange)
                {
                    movingRight = true;
                }
            }
        }
    }
}
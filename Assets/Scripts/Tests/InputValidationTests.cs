using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using System.Collections;
using MobileScrollingGame.Input;
using MobileScrollingGame.Core;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 输入验证和错误处理测试
    /// 测试输入验证、多点触控冲突解决和输入缓冲功能
    /// </summary>
    public class InputValidationTests
    {
        private GameObject testGameObject;
        private TouchInputHandler touchInputHandler;
        
        [SetUp]
        public void SetUp()
        {
            testGameObject = new GameObject("InputValidationTestObject");
            touchInputHandler = testGameObject.AddComponent<TouchInputHandler>();
            touchInputHandler.SetDebugMode(true);
        }
        
        [TearDown]
        public void TearDown()
        {
            if (testGameObject != null)
            {
                Object.DestroyImmediate(testGameObject);
            }
        }
        
        #region 输入验证测试
        
        [Test]
        public void InputValidation_InitialState_ShouldBeHealthy()
        {
            // 测试初始状态应该是健康的
            Assert.IsTrue(touchInputHandler.IsInputSystemHealthy());
            Assert.AreEqual(0, touchInputHandler.GetActiveTouchCount());
            Assert.AreEqual(0, touchInputHandler.GetInputBufferSize());
            Assert.AreEqual(0, touchInputHandler.GetInvalidInputCount());
        }
        
        [Test]
        public void InputValidation_ResetInputs_ShouldClearAllValidationData()
        {
            // 重置输入应该清理所有验证数据
            touchInputHandler.ResetInputs();
            
            Assert.IsTrue(touchInputHandler.IsInputSystemHealthy());
            Assert.AreEqual(0, touchInputHandler.GetActiveTouchCount());
            Assert.AreEqual(0, touchInputHandler.GetInputBufferSize());
            Assert.AreEqual(0, touchInputHandler.GetInvalidInputCount());
            Assert.AreEqual(0f, touchInputHandler.GetLastValidInputTime());
        }
        
        [Test]
        public void InputValidation_ForceCleanup_ShouldWork()
        {
            // 强制清理应该正常工作
            touchInputHandler.ForceCleanup();
            
            Assert.IsTrue(touchInputHandler.IsInputSystemHealthy());
        }
        
        [UnityTest]
        public IEnumerator InputValidation_InputBuffer_ShouldProcessOverTime()
        {
            // 等待几帧让输入缓冲系统工作
            yield return new WaitForSeconds(0.2f);
            
            // 输入缓冲应该随时间处理
            Assert.AreEqual(0, touchInputHandler.GetInputBufferSize());
        }
        
        #endregion
        
        #region 多点触控冲突解决测试
        
        [Test]
        public void MultiTouch_ConflictResolution_ShouldHandleMultipleTouches()
        {
            // 测试多点触控冲突解决
            // 由于我们无法直接模拟触摸事件，我们测试相关的状态
            
            Assert.AreEqual(0, touchInputHandler.GetActiveTouchCount());
            Assert.IsTrue(touchInputHandler.IsInputSystemHealthy());
        }
        
        [Test]
        public void MultiTouch_TouchTracking_ShouldTrackActiveTouches()
        {
            // 测试触摸跟踪
            int initialTouchCount = touchInputHandler.GetActiveTouchCount();
            Assert.AreEqual(0, initialTouchCount);
        }
        
        #endregion
        
        #region 输入缓冲测试
        
        [Test]
        public void InputBuffer_InitialState_ShouldBeEmpty()
        {
            // 输入缓冲初始状态应该为空
            Assert.AreEqual(0, touchInputHandler.GetInputBufferSize());
        }
        
        [UnityTest]
        public IEnumerator InputBuffer_Processing_ShouldWorkOverTime()
        {
            // 测试输入缓冲处理
            int initialBufferSize = touchInputHandler.GetInputBufferSize();
            
            // 等待输入缓冲处理
            yield return new WaitForSeconds(0.15f);
            
            // 缓冲区应该保持稳定
            Assert.AreEqual(initialBufferSize, touchInputHandler.GetInputBufferSize());
        }
        
        #endregion
        
        #region 错误处理测试
        
        [Test]
        public void ErrorHandling_InvalidInputCount_ShouldTrack()
        {
            // 测试无效输入计数跟踪
            int initialInvalidCount = touchInputHandler.GetInvalidInputCount();
            Assert.AreEqual(0, initialInvalidCount);
        }
        
        [Test]
        public void ErrorHandling_SystemHealth_ShouldMonitor()
        {
            // 测试系统健康监控
            Assert.IsTrue(touchInputHandler.IsInputSystemHealthy());
        }
        
        [Test]
        public void ErrorHandling_LastValidInputTime_ShouldTrack()
        {
            // 测试最后有效输入时间跟踪
            float lastValidTime = touchInputHandler.GetLastValidInputTime();
            Assert.AreEqual(0f, lastValidTime);
        }
        
        #endregion
        
        #region 持续触控和释放处理测试 (需求1-验收标准5,6)
        
        [Test]
        public void ContinuousInput_TouchTracking_ShouldWork()
        {
            // 需求1-验收标准5: 持续按住移动触控，角色应该继续朝该方向移动
            
            // 验证触摸跟踪功能存在（通过调用方法来验证它们可用）
            Assert.DoesNotThrow(() => touchInputHandler.GetActiveTouchCount());
            Assert.DoesNotThrow(() => touchInputHandler.IsMovementTouchActive());
            
            // 初始状态应该没有活动触摸
            Assert.AreEqual(0, touchInputHandler.GetActiveTouchCount());
            Assert.IsFalse(touchInputHandler.IsMovementTouchActive());
        }
        
        [Test]
        public void InputRelease_StateReset_ShouldWork()
        {
            // 需求1-验收标准6: 当玩家释放所有触控输入时，角色应该返回待机状态
            
            // 测试输入释放后状态重置
            touchInputHandler.ResetInputs();
            
            Assert.AreEqual(Vector2.zero, touchInputHandler.GetMovementInput());
            Assert.IsFalse(touchInputHandler.GetJumpInput());
            Assert.IsFalse(touchInputHandler.GetActionInput());
            Assert.IsFalse(touchInputHandler.HasActiveInput());
            Assert.IsFalse(touchInputHandler.IsMovementTouchActive());
        }
        
        [UnityTest]
        public IEnumerator ContinuousInput_BufferProcessing_ShouldMaintainState()
        {
            // 测试持续输入的缓冲处理
            
            // 等待输入系统处理
            yield return new WaitForSeconds(0.1f);
            
            // 验证输入缓冲系统正常工作
            Assert.IsTrue(touchInputHandler.IsInputSystemHealthy());
            Assert.AreEqual(0, touchInputHandler.GetInputBufferSize());
        }
        
        #endregion
        
        #region 集成测试
        
        [UnityTest]
        public IEnumerator InputValidation_Integration_ShouldWorkTogether()
        {
            // 集成测试：所有验证和错误处理功能应该协同工作
            
            // 等待系统初始化
            yield return null;
            
            // 验证所有组件正常工作
            Assert.IsTrue(touchInputHandler.IsInputSystemHealthy());
            Assert.AreEqual(0, touchInputHandler.GetActiveTouchCount());
            Assert.AreEqual(0, touchInputHandler.GetInputBufferSize());
            Assert.AreEqual(0, touchInputHandler.GetInvalidInputCount());
            
            // 执行强制清理
            touchInputHandler.ForceCleanup();
            
            // 系统应该仍然健康
            Assert.IsTrue(touchInputHandler.IsInputSystemHealthy());
            
            // 重置输入
            touchInputHandler.ResetInputs();
            
            // 所有状态应该被清理
            Assert.AreEqual(Vector2.zero, touchInputHandler.GetMovementInput());
            Assert.IsFalse(touchInputHandler.HasActiveInput());
        }
        
        [Test]
        public void InputValidation_PublicInterface_ShouldBeComplete()
        {
            // 验证公共接口完整性
            
            // IInputHandler接口方法（通过调用验证它们可用）
            Assert.DoesNotThrow(() => touchInputHandler.GetMovementInput());
            Assert.DoesNotThrow(() => touchInputHandler.GetJumpInput());
            Assert.DoesNotThrow(() => touchInputHandler.GetActionInput());
            Assert.DoesNotThrow(() => touchInputHandler.EnableInput(true));
            Assert.DoesNotThrow(() => touchInputHandler.HasActiveInput());
            Assert.DoesNotThrow(() => touchInputHandler.ResetInputs());
            
            // 验证和错误处理方法
            Assert.DoesNotThrow(() => touchInputHandler.GetActiveTouchCount());
            Assert.DoesNotThrow(() => touchInputHandler.GetInputBufferSize());
            Assert.DoesNotThrow(() => touchInputHandler.GetInvalidInputCount());
            Assert.DoesNotThrow(() => touchInputHandler.GetLastValidInputTime());
            Assert.DoesNotThrow(() => touchInputHandler.IsInputSystemHealthy());
            Assert.DoesNotThrow(() => touchInputHandler.ForceCleanup());
            
            // 其他公共方法
            Assert.DoesNotThrow(() => touchInputHandler.IsMovementTouchActive());
            Assert.DoesNotThrow(() => touchInputHandler.GetMovementTouchId());
            Assert.DoesNotThrow(() => touchInputHandler.SetDebugMode(false));
        }
        
        #endregion
    }
}
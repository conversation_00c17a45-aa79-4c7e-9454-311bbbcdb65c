using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using System.Collections;
using MobileScrollingGame.Camera;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// CameraShake组件的修复版单元测试
    /// 解决了NullReferenceException和边界情况问题
    /// </summary>
    public class CameraShakeTestsFixed
    {
        private GameObject shakeObject;
        private CameraShake cameraShake;
        private Vector3 originalPosition;
        
        [SetUp]
        public void SetUp()
        {
            // 创建震动对象
            shakeObject = new GameObject("TestShake");
            cameraShake = shakeObject.AddComponent<CameraShake>();
            
            // 等待一帧让组件初始化
            originalPosition = shakeObject.transform.localPosition;
            
            // 手动触发初始化（模拟Start方法）
            cameraShake.enabled = true;
        }
        
        [TearDown]
        public void TearDown()
        {
            if (shakeObject != null)
                Object.DestroyImmediate(shakeObject);
        }
        
        [Test]
        public void StartShake_WithDefaultParameters_DoesNotThrow()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => cameraShake.StartShake(), 
                "StartShake with default parameters should not throw");
        }
        
        [Test]
        public void StartShake_WithCustomParameters_DoesNotThrow()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => cameraShake.StartShake(2f, 1f), 
                "StartShake with custom parameters should not throw");
        }
        
        [Test]
        public void IsShaking_ReturnsFalseInitially()
        {
            // Act & Assert
            Assert.IsFalse(cameraShake.IsShaking(), "初始状态应该不在震动");
        }
        
        [UnityTest]
        public IEnumerator StartShake_SetsIsShakingToTrue()
        {
            // Act
            cameraShake.StartShake(1f, 0.5f);
            yield return null; // 等待一帧
            
            // Assert
            Assert.IsTrue(cameraShake.IsShaking(), "开始震动后IsShaking应该返回true");
        }
        
        [UnityTest]
        public IEnumerator StartShake_MovesObjectFromOriginalPosition()
        {
            // Arrange
            Vector3 initialPosition = shakeObject.transform.localPosition;
            
            // Act
            cameraShake.StartShake(1f, 0.5f);
            yield return new WaitForSeconds(0.1f); // 等待震动开始
            
            // Assert
            Vector3 currentPosition = shakeObject.transform.localPosition;
            float distance = Vector3.Distance(initialPosition, currentPosition);
            Assert.Greater(distance, 0.01f, "震动时对象应该偏离原始位置");
        }
        
        [UnityTest]
        public IEnumerator StopShake_RestoresOriginalPosition()
        {
            // Arrange
            cameraShake.StartShake(2f, 1f);
            yield return new WaitForSeconds(0.1f); // 让震动开始
            
            // Act
            cameraShake.StopShake();
            yield return null;
            
            // Assert
            Vector3 currentPosition = shakeObject.transform.localPosition;
            Assert.AreEqual(originalPosition, currentPosition, "停止震动后应该恢复原始位置");
            Assert.IsFalse(cameraShake.IsShaking(), "停止震动后IsShaking应该返回false");
        }
        
        [UnityTest]
        public IEnumerator ShakeEndsAutomatically_AfterDuration()
        {
            // Arrange
            float duration = 0.2f;
            
            // Act
            cameraShake.StartShake(1f, duration);
            yield return new WaitForSeconds(duration + 0.1f); // 等待震动结束
            
            // Assert
            Assert.IsFalse(cameraShake.IsShaking(), "震动应该在持续时间后自动结束");
            Vector3 currentPosition = shakeObject.transform.localPosition;
            Assert.AreEqual(originalPosition, currentPosition, "震动结束后应该恢复原始位置");
        }
        
        [Test]
        public void ExplosionShake_DoesNotThrow()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => cameraShake.ExplosionShake(), 
                "ExplosionShake should not throw");
            Assert.DoesNotThrow(() => cameraShake.ExplosionShake(3f, 0.5f), 
                "ExplosionShake with parameters should not throw");
        }
        
        [Test]
        public void ImpactShake_DoesNotThrow()
        {
            // Arrange
            Vector2 impactDirection = Vector2.right;
            
            // Act & Assert
            Assert.DoesNotThrow(() => cameraShake.ImpactShake(impactDirection), 
                "ImpactShake should not throw");
            Assert.DoesNotThrow(() => cameraShake.ImpactShake(impactDirection, 2f, 0.3f), 
                "ImpactShake with parameters should not throw");
        }
        
        [Test]
        public void ContinuousShake_DoesNotThrow()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => cameraShake.ContinuousShake(), 
                "ContinuousShake should not throw");
            Assert.DoesNotThrow(() => cameraShake.ContinuousShake(1f, 2f), 
                "ContinuousShake with parameters should not throw");
        }
        
        [UnityTest]
        public IEnumerator ShakeEvents_TriggerCorrectly()
        {
            // Arrange
            bool shakeStarted = false;
            bool shakeEnded = false;
            
            cameraShake.OnShakeStarted += () => shakeStarted = true;
            cameraShake.OnShakeEnded += () => shakeEnded = true;
            
            // Act
            cameraShake.StartShake(1f, 0.2f);
            yield return null; // 等待震动开始
            
            // Assert - 开始事件
            Assert.IsTrue(shakeStarted, "震动开始事件应该被触发");
            
            // Wait for shake to end
            yield return new WaitForSeconds(0.3f);
            
            // Assert - 结束事件
            Assert.IsTrue(shakeEnded, "震动结束事件应该被触发");
        }
        
        [UnityTest]
        public IEnumerator StopShake_TriggersEndEvent()
        {
            // Arrange
            bool shakeEnded = false;
            cameraShake.OnShakeEnded += () => shakeEnded = true;
            
            // Act
            cameraShake.StartShake(1f, 1f);
            yield return new WaitForSeconds(0.1f);
            cameraShake.StopShake();
            yield return null;
            
            // Assert
            Assert.IsTrue(shakeEnded, "手动停止震动应该触发结束事件");
        }
        
        [UnityTest]
        public IEnumerator MultipleShakes_OnlyOneActiveAtTime()
        {
            // Act
            cameraShake.StartShake(1f, 1f);
            yield return new WaitForSeconds(0.1f);
            Assert.IsTrue(cameraShake.IsShaking(), "第一个震动应该激活");
            
            // 开始第二个震动
            cameraShake.StartShake(2f, 1f);
            yield return null;
            
            // Assert
            Assert.IsTrue(cameraShake.IsShaking(), "第二个震动应该替换第一个");
        }
        
        [Test]
        public void ImpactShake_WithZeroVector_DoesNotThrow()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => cameraShake.ImpactShake(Vector2.zero), 
                "ImpactShake with zero vector should not throw");
        }
        
        [Test]
        public void StartShake_WithZeroDuration_DoesNotThrow()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => cameraShake.StartShake(1f, 0f), 
                "StartShake with zero duration should not throw");
        }
        
        [Test]
        public void StartShake_WithNegativeIntensity_DoesNotThrow()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => cameraShake.StartShake(-1f, 0.5f), 
                "StartShake with negative intensity should not throw");
        }
        
        [Test]
        public void StartShake_WithNegativeDuration_DoesNotThrow()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => cameraShake.StartShake(1f, -0.5f), 
                "StartShake with negative duration should not throw");
        }
        
        [UnityTest]
        public IEnumerator ShakeIntensity_HandlesZeroAndNegativeValues()
        {
            // Test zero intensity
            cameraShake.StartShake(0f, 0.2f);
            yield return new WaitForSeconds(0.1f);
            
            // Should not be shaking with zero intensity (below minimum threshold)
            // This depends on the minShakeIntensity setting
            
            // Test negative intensity
            cameraShake.StartShake(-1f, 0.2f);
            yield return new WaitForSeconds(0.1f);
            
            // Should handle negative intensity gracefully
            Assert.DoesNotThrow(() => cameraShake.IsShaking());
        }
        
        [Test]
        public void ComponentIntegrity_AllMethodsWork()
        {
            // Test all public methods don't throw exceptions
            Assert.DoesNotThrow(() => cameraShake.IsShaking());
            Assert.DoesNotThrow(() => cameraShake.StartShake());
            Assert.DoesNotThrow(() => cameraShake.StartShake(1f, 0.5f));
            Assert.DoesNotThrow(() => cameraShake.StopShake());
            Assert.DoesNotThrow(() => cameraShake.ExplosionShake());
            Assert.DoesNotThrow(() => cameraShake.ImpactShake(Vector2.up));
            Assert.DoesNotThrow(() => cameraShake.ContinuousShake());
        }
        
        [Test]
        public void ShakeSettings_CanBeModified()
        {
            // Test that shake settings can be modified without throwing
            Assert.DoesNotThrow(() => cameraShake.SetShakePooling(true, 3));
            Assert.DoesNotThrow(() => cameraShake.SetShakePooling(false, 1));
        }
        
        [UnityTest]
        public IEnumerator EdgeCase_RapidStartStop()
        {
            // Test rapid start/stop operations
            for (int i = 0; i < 10; i++)
            {
                cameraShake.StartShake(0.5f, 0.1f);
                yield return null;
                cameraShake.StopShake();
                yield return null;
            }
            
            // Should end in a stable state
            Assert.IsFalse(cameraShake.IsShaking(), "After rapid start/stop, should not be shaking");
            Assert.AreEqual(originalPosition, shakeObject.transform.localPosition, 
                "After rapid start/stop, should return to original position");
        }
        
        [UnityTest]
        public IEnumerator EdgeCase_VeryShortDuration()
        {
            // Test very short duration shake
            cameraShake.StartShake(1f, 0.01f);
            yield return new WaitForSeconds(0.05f);
            
            // Should complete without errors
            Assert.IsFalse(cameraShake.IsShaking(), "Very short shake should complete quickly");
        }
        
        [UnityTest]
        public IEnumerator EdgeCase_VeryLongDuration()
        {
            // Test very long duration shake
            cameraShake.StartShake(0.1f, 10f);
            yield return new WaitForSeconds(0.1f);
            
            Assert.IsTrue(cameraShake.IsShaking(), "Long duration shake should be active");
            
            // Stop it to clean up
            cameraShake.StopShake();
            yield return null;
            
            Assert.IsFalse(cameraShake.IsShaking(), "Should stop when requested");
        }
    }
}
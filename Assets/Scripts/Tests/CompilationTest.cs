using NUnit.Framework;
using UnityEngine;
using MobileScrollingGame.Player;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 编译测试 - 验证命名冲突是否已解决
    /// </summary>
    public class CompilationTest
    {
        [Test]
        public void CharacterController_NamespaceResolution_Works()
        {
            // 这个测试主要是为了验证编译时没有命名冲突
            // 如果有命名冲突，这个文件就无法编译
            
            GameObject testObject = new GameObject("CompilationTest");
            
            // 测试我们可以正确引用自定义的CharacterController
            var customController = testObject.AddComponent<MobileScrollingGame.Player.CharacterController>();
            Assert.IsNotNull(customController, "应该能够创建自定义的CharacterController");
            
            // 测试我们也可以引用Unity的CharacterController（如果需要的话）
            var unityController = testObject.AddComponent<UnityEngine.CharacterController>();
            Assert.IsNotNull(unityController, "应该能够创建Unity的CharacterController");
            
            // 清理
            Object.DestroyImmediate(testObject);
        }
        
        [Test]
        public void CharacterAnimator_CanBeCreated()
        {
            GameObject testObject = new GameObject("AnimatorTest");
            
            var animator = testObject.AddComponent<CharacterAnimator>();
            Assert.IsNotNull(animator, "应该能够创建CharacterAnimator");
            
            Object.DestroyImmediate(testObject);
        }
        
        [Test]
        public void CharacterData_CanBeInstantiated()
        {
            var data = new CharacterData
            {
                moveSpeed = 5f,
                jumpForce = 10f,
                maxHealth = 100,
                currentHealth = 100,
                isGrounded = true,
                facingRight = true
            };
            
            Assert.IsNotNull(data, "应该能够创建CharacterData");
            Assert.AreEqual(5f, data.moveSpeed, "数据应该正确设置");
        }
    }
}
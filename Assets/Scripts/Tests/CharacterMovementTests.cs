using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using System.Collections;
using MobileScrollingGame.Player;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 角色移动系统的单元测试
    /// 测试基础移动、跳跃和地面检测功能
    /// </summary>
    public class CharacterMovementTests
    {
        private GameObject testCharacter;
        private CharacterMovement characterMovement;
        private CharacterData testData;
        private Rigidbody2D rb2d;
        
        [SetUp]
        public void SetUp()
        {
            // 创建测试角色对象
            testCharacter = new GameObject("TestCharacter");
            
            // 添加必要的组件
            rb2d = testCharacter.AddComponent<Rigidbody2D>();
            testCharacter.AddComponent<BoxCollider2D>();
            characterMovement = testCharacter.AddComponent<CharacterMovement>();
            
            // 创建测试数据
            testData = new CharacterData
            {
                moveSpeed = 5f,
                jumpForce = 10f,
                maxHealth = 100,
                currentHealth = 100,
                groundCheckDistance = 0.1f,
                groundLayerMask = 1
            };
            
            characterMovement.SetCharacterData(testData);
        }
        
        [TearDown]
        public void TearDown()
        {
            if (testCharacter != null)
            {
                Object.DestroyImmediate(testCharacter);
            }
        }
        
        [Test]
        public void CharacterData_IsValid_ReturnsTrue()
        {
            // Arrange & Act & Assert
            Assert.IsTrue(testData.IsValid(), "角色数据应该是有效的");
        }
        
        [Test]
        public void CharacterData_InvalidMoveSpeed_ReturnsFalse()
        {
            // Arrange
            testData.moveSpeed = -1f;
            
            // Act & Assert
            Assert.IsFalse(testData.IsValid(), "负的移动速度应该使数据无效");
        }
        
        [Test]
        public void SetMoveInput_ValidInput_UpdatesMovement()
        {
            // Arrange
            Vector2 moveInput = new Vector2(1f, 0f);
            
            // Act
            characterMovement.SetMoveInput(moveInput);
            
            // Assert
            // 由于移动在FixedUpdate中应用，我们需要等待一帧
            Assert.IsNotNull(characterMovement, "角色移动组件应该存在");
        }
        
        [UnityTest]
        public IEnumerator SetMoveInput_RightMovement_MovesCharacterRight()
        {
            // Arrange
            Vector3 initialPosition = testCharacter.transform.position;
            Vector2 rightInput = new Vector2(1f, 0f);
            
            // Act
            characterMovement.SetMoveInput(rightInput);
            
            // 等待几个物理帧
            yield return new WaitForFixedUpdate();
            yield return new WaitForFixedUpdate();
            
            // Assert
            Assert.Greater(rb2d.linearVelocity.x, 0f, "角色应该向右移动");
        }
        
        [UnityTest]
        public IEnumerator SetMoveInput_LeftMovement_MovesCharacterLeft()
        {
            // Arrange
            Vector2 leftInput = new Vector2(-1f, 0f);
            
            // Act
            characterMovement.SetMoveInput(leftInput);
            
            // 等待几个物理帧
            yield return new WaitForFixedUpdate();
            yield return new WaitForFixedUpdate();
            
            // Assert
            Assert.Less(rb2d.linearVelocity.x, 0f, "角色应该向左移动");
        }
        
        [Test]
        public void TryJump_WhenNotGrounded_ReturnsFalse()
        {
            // Arrange - 角色默认不在地面上
            
            // Act
            bool jumpResult = characterMovement.TryJump();
            
            // Assert
            Assert.IsFalse(jumpResult, "不在地面时跳跃应该失败");
        }
        
        [Test]
        public void StopMovement_ClearsMovementInput()
        {
            // Arrange
            characterMovement.SetMoveInput(new Vector2(1f, 0f));
            
            // Act
            characterMovement.StopMovement();
            
            // Assert
            // 验证移动已停止（通过检查速度在下一个物理更新后是否为0）
            Assert.IsNotNull(characterMovement, "角色移动组件应该存在");
        }
        
        [UnityTest]
        public IEnumerator StopMovement_StopsCharacterMovement()
        {
            // Arrange
            characterMovement.SetMoveInput(new Vector2(1f, 0f));
            yield return new WaitForFixedUpdate();
            
            // Act
            characterMovement.StopMovement();
            yield return new WaitForFixedUpdate();
            yield return new WaitForFixedUpdate();
            
            // Assert
            Assert.AreEqual(0f, rb2d.linearVelocity.x, 0.1f, "停止移动后角色应该不再水平移动");
        }
        
        [Test]
        public void FacingRight_InitialState_IsTrue()
        {
            // Assert
            Assert.IsTrue(characterMovement.FacingRight, "角色初始应该面向右侧");
        }
        
        [UnityTest]
        public IEnumerator Move_LeftInput_FlipsCharacterToLeft()
        {
            // Arrange
            Vector2 leftInput = new Vector2(-1f, 0f);
            
            // Act
            characterMovement.SetMoveInput(leftInput);
            yield return new WaitForFixedUpdate();
            yield return new WaitForFixedUpdate();
            
            // Assert
            Assert.IsFalse(characterMovement.FacingRight, "向左移动时角色应该面向左侧");
        }
        
        [UnityTest]
        public IEnumerator Move_RightInput_KeepsCharacterFacingRight()
        {
            // Arrange
            Vector2 rightInput = new Vector2(1f, 0f);
            
            // Act
            characterMovement.SetMoveInput(rightInput);
            yield return new WaitForFixedUpdate();
            yield return new WaitForFixedUpdate();
            
            // Assert
            Assert.IsTrue(characterMovement.FacingRight, "向右移动时角色应该保持面向右侧");
        }
        
        [Test]
        public void GetCharacterData_ReturnsCorrectData()
        {
            // Act
            CharacterData retrievedData = characterMovement.GetCharacterData();
            
            // Assert
            Assert.AreEqual(testData.moveSpeed, retrievedData.moveSpeed, "移动速度应该匹配");
            Assert.AreEqual(testData.jumpForce, retrievedData.jumpForce, "跳跃力度应该匹配");
            Assert.AreEqual(testData.maxHealth, retrievedData.maxHealth, "最大生命值应该匹配");
        }
        
        [Test]
        public void SetCharacterData_UpdatesMovementParameters()
        {
            // Arrange
            CharacterData newData = new CharacterData
            {
                moveSpeed = 8f,
                jumpForce = 15f,
                maxHealth = 150
            };
            
            // Act
            characterMovement.SetCharacterData(newData);
            CharacterData retrievedData = characterMovement.GetCharacterData();
            
            // Assert
            Assert.AreEqual(8f, retrievedData.moveSpeed, "新的移动速度应该被设置");
            Assert.AreEqual(15f, retrievedData.jumpForce, "新的跳跃力度应该被设置");
            Assert.AreEqual(150, retrievedData.maxHealth, "新的最大生命值应该被设置");
        }
    }
}
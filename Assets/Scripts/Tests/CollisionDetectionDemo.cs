using UnityEngine;
using MobileScrollingGame.Player;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 碰撞检测演示脚本
    /// 用于在编辑器中手动测试和验证碰撞检测功能
    /// 满足需求3.5和3.6的验收标准
    /// </summary>
    public class CollisionDetectionDemo : MonoBehaviour
    {
        [Header("演示设置")]
        [SerializeField] private bool enableDebugOutput = true;
        [SerializeField] private bool showCollisionFeedback = true;
        
        [Header("碰撞响应设置")]
        [SerializeField] private float obstacleResponseForce = 5f;
        [SerializeField] private float platformSnapDistance = 0.1f;
        
        // 组件引用
        private CollisionDetector collisionDetector;
        private PlatformCollisionHandler platformHandler;
        private MobileScrollingGame.Player.CharacterController characterController;
        private Rigidbody2D rb2d;
        
        // 状态跟踪
        private bool wasGrounded;
        private bool wasOnPlatform;
        private bool wasAgainstWall;
        private bool wasNearObstacle;
        
        private void Awake()
        {
            InitializeComponents();
        }
        
        private void Start()
        {
            SubscribeToEvents();
            LogInitialState();
        }
        
        private void Update()
        {
            CheckStateChanges();
            HandleCollisionResponses();
        }
        
        private void OnDestroy()
        {
            UnsubscribeFromEvents();
        }
        
        /// <summary>
        /// 初始化组件引用
        /// </summary>
        private void InitializeComponents()
        {
            collisionDetector = GetComponent<CollisionDetector>();
            platformHandler = GetComponent<PlatformCollisionHandler>();
            characterController = GetComponent<MobileScrollingGame.Player.CharacterController>();
            rb2d = GetComponent<Rigidbody2D>();
            
            if (collisionDetector == null)
            {
                Debug.LogError("CollisionDetectionDemo: 需要CollisionDetector组件");
            }
        }
        
        /// <summary>
        /// 订阅碰撞事件
        /// </summary>
        private void SubscribeToEvents()
        {
            if (collisionDetector != null)
            {
                collisionDetector.OnGroundedChanged += OnGroundedChanged;
                collisionDetector.OnWallCollisionChanged += OnWallCollisionChanged;
                collisionDetector.OnObstacleDetected += OnObstacleDetected;
                collisionDetector.OnBoundaryChanged += OnBoundaryChanged;
                collisionDetector.OnCollisionEnter += OnCollisionEnter;
                collisionDetector.OnCollisionExit += OnCollisionExit;
            }
            
            if (platformHandler != null)
            {
                platformHandler.OnPlatformEnter += OnPlatformEnter;
                platformHandler.OnPlatformExit += OnPlatformExit;
                platformHandler.OnDropThroughPlatform += OnDropThroughPlatform;
                platformHandler.OnMovingPlatformMove += OnMovingPlatformMove;
            }
        }
        
        /// <summary>
        /// 取消订阅碰撞事件
        /// </summary>
        private void UnsubscribeFromEvents()
        {
            if (collisionDetector != null)
            {
                collisionDetector.OnGroundedChanged -= OnGroundedChanged;
                collisionDetector.OnWallCollisionChanged -= OnWallCollisionChanged;
                collisionDetector.OnObstacleDetected -= OnObstacleDetected;
                collisionDetector.OnBoundaryChanged -= OnBoundaryChanged;
                collisionDetector.OnCollisionEnter -= OnCollisionEnter;
                collisionDetector.OnCollisionExit -= OnCollisionExit;
            }
            
            if (platformHandler != null)
            {
                platformHandler.OnPlatformEnter -= OnPlatformEnter;
                platformHandler.OnPlatformExit -= OnPlatformExit;
                platformHandler.OnDropThroughPlatform -= OnDropThroughPlatform;
                platformHandler.OnMovingPlatformMove -= OnMovingPlatformMove;
            }
        }
        
        /// <summary>
        /// 记录初始状态
        /// </summary>
        private void LogInitialState()
        {
            if (!enableDebugOutput) return;
            
            Debug.Log("=== 碰撞检测演示开始 ===");
            Debug.Log($"初始位置: {transform.position}");
            Debug.Log($"地面检测: {collisionDetector?.IsGrounded}");
            Debug.Log($"平台检测: {platformHandler?.IsOnPlatform}");
        }
        
        /// <summary>
        /// 检查状态变化
        /// </summary>
        private void CheckStateChanges()
        {
            if (collisionDetector == null) return;
            
            // 检查地面状态变化
            if (wasGrounded != collisionDetector.IsGrounded)
            {
                wasGrounded = collisionDetector.IsGrounded;
                LogStateChange("地面状态", wasGrounded);
            }
            
            // 检查墙壁状态变化
            if (wasAgainstWall != collisionDetector.IsAgainstWall)
            {
                wasAgainstWall = collisionDetector.IsAgainstWall;
                LogStateChange("墙壁碰撞", wasAgainstWall);
            }
            
            // 检查障碍物状态变化
            if (wasNearObstacle != collisionDetector.IsNearObstacle)
            {
                wasNearObstacle = collisionDetector.IsNearObstacle;
                LogStateChange("障碍物检测", wasNearObstacle);
            }
            
            // 检查平台状态变化
            if (platformHandler != null && wasOnPlatform != platformHandler.IsOnPlatform)
            {
                wasOnPlatform = platformHandler.IsOnPlatform;
                LogStateChange("平台状态", wasOnPlatform);
            }
        }
        
        /// <summary>
        /// 处理碰撞响应 - 满足需求3.6
        /// </summary>
        private void HandleCollisionResponses()
        {
            if (collisionDetector == null || rb2d == null) return;
            
            // 障碍物碰撞响应
            if (collisionDetector.IsNearObstacle)
            {
                HandleObstacleCollisionResponse();
            }
            
            // 平台交互响应 - 满足需求3.5
            if (platformHandler != null && platformHandler.IsOnPlatform)
            {
                HandlePlatformInteractionResponse();
            }
            
            // 墙壁碰撞响应
            if (collisionDetector.IsAgainstWall)
            {
                HandleWallCollisionResponse();
            }
        }
        
        /// <summary>
        /// 处理障碍物碰撞响应
        /// </summary>
        private void HandleObstacleCollisionResponse()
        {
            if (!showCollisionFeedback) return;
            
            // 获取障碍物碰撞信息
            RaycastHit2D obstacleHit = collisionDetector.ObstacleHit;
            if (obstacleHit.collider != null)
            {
                // 计算反弹方向
                Vector2 pushDirection = ((Vector2)transform.position - obstacleHit.point).normalized;
                
                // 应用轻微的推力作为碰撞响应
                rb2d.AddForce(pushDirection * obstacleResponseForce, ForceMode2D.Impulse);
                
                if (enableDebugOutput)
                {
                    Debug.Log($"障碍物碰撞响应: 推力方向 {pushDirection}, 强度 {obstacleResponseForce}");
                }
            }
        }
        
        /// <summary>
        /// 处理平台交互响应
        /// </summary>
        private void HandlePlatformInteractionResponse()
        {
            if (platformHandler.CurrentPlatform == null) return;
            
            // 确保角色正确贴合平台表面
            Bounds platformBounds = platformHandler.CurrentPlatform.bounds;
            float targetY = platformBounds.max.y + GetComponent<Collider2D>().bounds.extents.y;
            
            if (Mathf.Abs(transform.position.y - targetY) > platformSnapDistance)
            {
                Vector3 snapPosition = transform.position;
                snapPosition.y = targetY;
                transform.position = snapPosition;
                
                if (enableDebugOutput)
                {
                    Debug.Log($"平台交互响应: 角色位置调整到 {snapPosition}");
                }
            }
        }
        
        /// <summary>
        /// 处理墙壁碰撞响应
        /// </summary>
        private void HandleWallCollisionResponse()
        {
            // 阻止角色穿过墙壁
            RaycastHit2D wallHit = collisionDetector.WallHit;
            if (wallHit.collider != null)
            {
                // 如果角色试图向墙壁方向移动，停止水平速度
                Vector2 velocity = rb2d.linearVelocity;
                if ((wallHit.normal.x > 0 && velocity.x < 0) || (wallHit.normal.x < 0 && velocity.x > 0))
                {
                    velocity.x = 0;
                    rb2d.linearVelocity = velocity;
                    
                    if (enableDebugOutput)
                    {
                        Debug.Log("墙壁碰撞响应: 停止水平移动");
                    }
                }
            }
        }
        
        /// <summary>
        /// 记录状态变化
        /// </summary>
        private void LogStateChange(string stateName, bool newState)
        {
            if (!enableDebugOutput) return;
            
            string status = newState ? "激活" : "取消";
            Debug.Log($"[碰撞检测] {stateName} {status} - 时间: {Time.time:F2}s");
        }
        
        #region 事件处理器
        
        private void OnGroundedChanged(bool grounded)
        {
            if (enableDebugOutput)
            {
                Debug.Log($"[事件] 地面状态改变: {grounded}");
            }
            
            if (showCollisionFeedback && grounded)
            {
                // 着陆时的视觉反馈
                Debug.Log("着陆检测 - 平台交互正常工作 (需求3.5)");
            }
        }
        
        private void OnWallCollisionChanged(bool againstWall)
        {
            if (enableDebugOutput)
            {
                Debug.Log($"[事件] 墙壁碰撞状态改变: {againstWall}");
            }
        }
        
        private void OnObstacleDetected(bool nearObstacle)
        {
            if (enableDebugOutput)
            {
                Debug.Log($"[事件] 障碍物检测状态改变: {nearObstacle}");
            }
            
            if (showCollisionFeedback && nearObstacle)
            {
                Debug.Log("障碍物碰撞响应触发 (需求3.6)");
            }
        }
        
        private void OnBoundaryChanged(bool outOfBounds)
        {
            if (enableDebugOutput)
            {
                Debug.Log($"[事件] 边界状态改变: {outOfBounds}");
            }
            
            if (outOfBounds)
            {
                Debug.LogWarning("角色超出游戏边界！");
            }
        }
        
        private void OnCollisionEnter(Collision2D collision)
        {
            if (enableDebugOutput)
            {
                Debug.Log($"[事件] 碰撞进入: {collision.gameObject.name}");
            }
        }
        
        private void OnCollisionExit(Collision2D collision)
        {
            if (enableDebugOutput)
            {
                Debug.Log($"[事件] 碰撞退出: {collision.gameObject.name}");
            }
        }
        
        private void OnPlatformEnter(Collider2D platform)
        {
            if (enableDebugOutput)
            {
                Debug.Log($"[事件] 进入平台: {platform.name}");
            }
            
            if (showCollisionFeedback)
            {
                Debug.Log("平台交互检测正常 (需求3.5)");
            }
        }
        
        private void OnPlatformExit(Collider2D platform)
        {
            if (enableDebugOutput)
            {
                Debug.Log($"[事件] 离开平台: {platform.name}");
            }
        }
        
        private void OnDropThroughPlatform()
        {
            if (enableDebugOutput)
            {
                Debug.Log("[事件] 穿透平台");
            }
        }
        
        private void OnMovingPlatformMove(Vector3 influence)
        {
            if (enableDebugOutput)
            {
                Debug.Log($"[事件] 移动平台影响: {influence}");
            }
        }
        
        #endregion
        
        #region 公共接口 - 用于测试
        
        /// <summary>
        /// 获取当前碰撞状态摘要
        /// </summary>
        public string GetCollisionStatusSummary()
        {
            if (collisionDetector == null) return "碰撞检测器未找到";
            
            return $"地面: {collisionDetector.IsGrounded}, " +
                   $"平台: {platformHandler?.IsOnPlatform}, " +
                   $"墙壁: {collisionDetector.IsAgainstWall}, " +
                   $"障碍物: {collisionDetector.IsNearObstacle}, " +
                   $"边界外: {collisionDetector.IsOutOfBounds}";
        }
        
        /// <summary>
        /// 验证需求3.5 - 平台交互准确性
        /// </summary>
        public bool ValidatePlatformInteractionAccuracy()
        {
            if (collisionDetector == null || platformHandler == null) return false;
            
            // 检查平台检测的一致性
            bool groundDetected = collisionDetector.IsGrounded;
            bool platformDetected = platformHandler.IsOnPlatform;
            
            // 如果在平台上，应该也检测到地面
            if (platformDetected && !groundDetected)
            {
                Debug.LogWarning("平台交互不准确：在平台上但未检测到地面");
                return false;
            }
            
            return true;
        }
        
        /// <summary>
        /// 验证需求3.6 - 障碍物碰撞响应
        /// </summary>
        public bool ValidateObstacleCollisionResponse()
        {
            if (collisionDetector == null) return false;
            
            // 检查障碍物检测时是否能正确阻止移动
            if (collisionDetector.IsNearObstacle)
            {
                bool canMoveToObstacle = collisionDetector.CanMoveInDirection(Vector2.right) || 
                                       collisionDetector.CanMoveInDirection(Vector2.left);
                
                if (canMoveToObstacle)
                {
                    Debug.LogWarning("障碍物碰撞响应不正确：检测到障碍物但仍允许移动");
                    return false;
                }
            }
            
            return true;
        }
        
        #endregion
        
        #region 调试可视化
        
        private void OnDrawGizmos()
        {
            if (!showCollisionFeedback || collisionDetector == null) return;
            
            // 绘制碰撞状态指示器
            Vector3 position = transform.position;
            
            // 地面状态
            Gizmos.color = collisionDetector.IsGrounded ? Color.green : Color.red;
            Gizmos.DrawWireSphere(position + Vector3.down * 2f, 0.2f);
            
            // 墙壁状态
            Gizmos.color = collisionDetector.IsAgainstWall ? Color.red : Color.white;
            Gizmos.DrawWireSphere(position + Vector3.right * 2f, 0.2f);
            
            // 障碍物状态
            Gizmos.color = collisionDetector.IsNearObstacle ? Color.yellow : Color.white;
            Gizmos.DrawWireSphere(position + Vector3.up * 2f, 0.2f);
            
            // 平台状态
            if (platformHandler != null)
            {
                Gizmos.color = platformHandler.IsOnPlatform ? Color.blue : Color.white;
                Gizmos.DrawWireSphere(position + Vector3.left * 2f, 0.2f);
            }
        }
        
        #endregion
    }
}
using UnityEngine;
using MobileScrollingGame.Camera;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// CameraFollower调试测试脚本
    /// 用于诊断和修复测试失败问题
    /// </summary>
    public class CameraFollowerDebugTest : MonoBehaviour
    {
        [Header("调试设置")]
        [SerializeField] private bool enableDebugLog = true;
        
        private void Start()
        {
            if (enableDebugLog)
            {
                RunDebugTests();
            }
        }
        
        private void RunDebugTests()
        {
            Debug.Log("=== CameraFollower 调试测试开始 ===");
            
            // 测试1: 边界限制问题
            TestBoundaryLimiting();
            
            // 测试2: EnableBounds功能
            TestEnableBounds();
            
            // 测试3: 跟随移动距离
            TestFollowMovement();
            
            Debug.Log("=== CameraFollower 调试测试完成 ===");
        }
        
        private void TestBoundaryLimiting()
        {
            Debug.Log("--- 测试边界限制功能 ---");
            
            // 创建测试环境
            GameObject cameraObj = new GameObject("DebugCamera");
            UnityEngine.Camera cam = cameraObj.AddComponent<UnityEngine.Camera>();
            cam.orthographic = true;
            cam.orthographicSize = 5f;
            cam.aspect = 16f / 9f;
            
            CameraFollower follower = cameraObj.AddComponent<CameraFollower>();
            
            GameObject target = new GameObject("DebugTarget");
            target.transform.position = Vector3.zero;
            
            // 设置边界
            Rect bounds = new Rect(-5, -3, 10, 6);
            follower.SetCameraBounds(bounds);
            follower.SetFollowTarget(target.transform);
            
            // 计算预期边界
            float cameraHeight = cam.orthographicSize * 2f;
            float cameraWidth = cameraHeight * cam.aspect;
            float expectedMaxX = bounds.xMax - cameraWidth * 0.5f;
            
            Debug.Log($"边界设置: {bounds}");
            Debug.Log($"摄像机尺寸: {cameraWidth}x{cameraHeight}");
            Debug.Log($"预期最大X: {expectedMaxX}");
            
            // 移动目标到边界外
            target.transform.position = new Vector3(20, 0, 0);
            follower.UpdateCameraPosition();
            
            Vector3 cameraPos = cameraObj.transform.position;
            Debug.Log($"目标位置: {target.transform.position}");
            Debug.Log($"摄像机位置: {cameraPos}");
            Debug.Log($"是否超出边界: {cameraPos.x > expectedMaxX}");
            
            // 清理
            DestroyImmediate(cameraObj);
            DestroyImmediate(target);
        }
        
        private void TestEnableBounds()
        {
            Debug.Log("--- 测试EnableBounds功能 ---");
            
            // 创建测试环境
            GameObject cameraObj = new GameObject("DebugCamera");
            UnityEngine.Camera cam = cameraObj.AddComponent<UnityEngine.Camera>();
            cam.orthographic = true;
            cam.orthographicSize = 5f;
            
            CameraFollower follower = cameraObj.AddComponent<CameraFollower>();
            
            GameObject target = new GameObject("DebugTarget");
            target.transform.position = Vector3.zero;
            
            // 设置边界和目标
            Rect bounds = new Rect(-2, -2, 4, 4);
            follower.SetCameraBounds(bounds);
            follower.SetFollowTarget(target.transform);
            
            // 移动目标到边界外
            target.transform.position = new Vector3(20, 0, 0);
            
            Debug.Log($"目标位置: {target.transform.position}");
            Debug.Log($"边界: {bounds}");
            
            // 启用边界
            follower.EnableBounds(true);
            Vector3 boundedPos = cameraObj.transform.position;
            Debug.Log($"启用边界后摄像机位置: {boundedPos}");
            
            // 禁用边界
            follower.EnableBounds(false);
            Vector3 unboundedPos = cameraObj.transform.position;
            Debug.Log($"禁用边界后摄像机位置: {unboundedPos}");
            
            Vector3 expectedPos = target.transform.position + new Vector3(0, 1, -10);
            Debug.Log($"期望位置: {expectedPos}");
            Debug.Log($"位置差异: {Vector3.Distance(expectedPos, unboundedPos)}");
            
            // 清理
            DestroyImmediate(cameraObj);
            DestroyImmediate(target);
        }
        
        private void TestFollowMovement()
        {
            Debug.Log("--- 测试跟随移动距离 ---");
            
            // 创建测试环境
            GameObject cameraObj = new GameObject("DebugCamera");
            UnityEngine.Camera cam = cameraObj.AddComponent<UnityEngine.Camera>();
            cam.orthographic = true;
            cam.orthographicSize = 5f;
            
            CameraFollower follower = cameraObj.AddComponent<CameraFollower>();
            follower.EnableBounds(false); // 禁用边界
            follower.SetFollowSpeed(10f);
            
            GameObject target = new GameObject("DebugTarget");
            target.transform.position = Vector3.zero;
            
            follower.SetFollowTarget(target.transform);
            Vector3 initialPos = cameraObj.transform.position;
            
            Debug.Log($"初始摄像机位置: {initialPos}");
            
            // 移动目标
            target.transform.position = new Vector3(5, 0, 0);
            Debug.Log($"目标移动到: {target.transform.position}");
            
            // 多次更新
            for (int i = 0; i < 5; i++)
            {
                follower.UpdateCameraPosition();
                Vector3 currentPos = cameraObj.transform.position;
                Debug.Log($"更新{i+1}次后位置: {currentPos}, 移动距离: {currentPos.x - initialPos.x}");
            }
            
            Vector3 finalPos = cameraObj.transform.position;
            float totalMovement = finalPos.x - initialPos.x;
            Debug.Log($"总移动距离: {totalMovement}");
            Debug.Log($"是否满足测试要求(>0.1): {totalMovement > 0.1f}");
            
            // 清理
            DestroyImmediate(cameraObj);
            DestroyImmediate(target);
        }
    }
}
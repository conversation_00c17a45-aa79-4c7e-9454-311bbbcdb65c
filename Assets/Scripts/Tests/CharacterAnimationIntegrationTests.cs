using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using System.Collections;
using MobileScrollingGame.Player;
using MobileScrollingGame.Core;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 角色动画系统集成测试
    /// 测试角色控制器与动画系统的完整集成
    /// </summary>
    public class CharacterAnimationIntegrationTests
    {
        private GameObject testCharacter;
        private MobileScrollingGame.Player.CharacterController characterController;
        private CharacterAnimator characterAnimator;
        private CharacterMovement characterMovement;
        private Animator animator;
        private SpriteRenderer spriteRenderer;
        
        [SetUp]
        public void SetUp()
        {
            // 创建测试角色
            testCharacter = new GameObject("TestCharacter");
            
            // 添加必要的组件
            testCharacter.AddComponent<Rigidbody2D>();
            testCharacter.AddComponent<BoxCollider2D>();
            animator = testCharacter.AddComponent<Animator>();
            spriteRenderer = testCharacter.AddComponent<SpriteRenderer>();
            
            // 添加角色系统组件
            characterMovement = testCharacter.AddComponent<CharacterMovement>();
            characterAnimator = testCharacter.AddComponent<CharacterAnimator>();
            characterController = testCharacter.AddComponent<MobileScrollingGame.Player.CharacterController>();
            
            // 等待组件初始化
            // Unity会自动调用Awake和Start方法
        }
        
        [TearDown]
        public void TearDown()
        {
            if (testCharacter != null)
            {
                Object.DestroyImmediate(testCharacter);
            }
        }
        
        [Test]
        public void Integration_CharacterControllerWithAnimator_InitializesCorrectly()
        {
            // Assert
            Assert.IsNotNull(characterController, "CharacterController应该存在");
            Assert.IsNotNull(characterAnimator, "CharacterAnimator应该存在");
            Assert.IsNotNull(characterMovement, "CharacterMovement应该存在");
            
            // 验证组件之间的连接
            Assert.IsTrue(characterAnimator.FacingRight, "角色初始应该面向右侧");
        }
        
        [Test]
        public void Integration_MoveCommand_TriggersCorrectAnimation()
        {
            // Arrange
            bool animationEventTriggered = false;
            string receivedAnimation = "";
            
            characterController.OnAnimationChanged += (animName) => 
            {
                animationEventTriggered = true;
                receivedAnimation = animName;
            };
            
            // Act
            characterController.Move(new Vector2(1f, 0f));
            
            // Assert
            Assert.IsTrue(animationEventTriggered, "移动应该触发动画事件");
            // 注意：具体的动画名称可能因为自动状态选择而不同
        }
        
        [Test]
        public void Integration_JumpCommand_TriggersJumpAnimation()
        {
            // Arrange
            bool animationEventTriggered = false;
            string receivedAnimation = "";
            
            characterController.OnAnimationChanged += (animName) => 
            {
                animationEventTriggered = true;
                receivedAnimation = animName;
            };
            
            // Act
            characterController.Jump();
            
            // Assert
            Assert.IsTrue(animationEventTriggered, "跳跃应该触发动画事件");
            Assert.AreEqual("Jumping", receivedAnimation, "应该播放跳跃动画");
        }
        
        [Test]
        public void Integration_ActionCommand_TriggersActionAnimation()
        {
            // Arrange
            bool animationEventTriggered = false;
            string receivedAnimation = "";
            
            characterController.OnAnimationChanged += (animName) => 
            {
                animationEventTriggered = true;
                receivedAnimation = animName;
            };
            
            // Act
            characterController.PerformAction();
            
            // Assert
            Assert.IsTrue(animationEventTriggered, "动作应该触发动画事件");
            Assert.AreEqual("Action", receivedAnimation, "应该播放动作动画");
        }
        
        [Test]
        public void Integration_IdleCommand_TriggersIdleAnimation()
        {
            // Arrange
            bool animationEventTriggered = false;
            string receivedAnimation = "";
            
            characterController.OnAnimationChanged += (animName) => 
            {
                animationEventTriggered = true;
                receivedAnimation = animName;
            };
            
            // Act
            characterController.SetIdleState();
            
            // Assert
            Assert.IsTrue(animationEventTriggered, "设置待机状态应该触发动画事件");
            Assert.AreEqual("Idle", receivedAnimation, "应该播放待机动画");
        }
        
        [UnityTest]
        public IEnumerator Integration_MovementDirection_UpdatesFacingDirection()
        {
            // Arrange
            bool facingChangedEventTriggered = false;
            bool finalFacingDirection = true;
            
            characterAnimator.OnFacingDirectionChanged += (facingRight) => 
            {
                facingChangedEventTriggered = true;
                finalFacingDirection = facingRight;
            };
            
            // Act - 向左移动
            characterController.Move(new Vector2(-1f, 0f));
            
            // 等待几个物理帧让移动系统处理
            yield return new WaitForFixedUpdate();
            yield return new WaitForFixedUpdate();
            
            // Assert
            Assert.IsTrue(facingChangedEventTriggered, "朝向改变应该触发事件");
            Assert.IsFalse(finalFacingDirection, "向左移动应该使角色面向左侧");
            Assert.IsFalse(characterAnimator.FacingRight, "动画器应该反映正确的朝向");
            Assert.IsTrue(spriteRenderer.flipX, "精灵应该被翻转");
        }
        
        [UnityTest]
        public IEnumerator Integration_GroundedStateChange_TriggersLandingAnimation()
        {
            // 这个测试模拟角色从空中着陆的情况
            
            // Arrange
            bool animationEventTriggered = false;
            string receivedAnimation = "";
            
            characterController.OnAnimationChanged += (animName) => 
            {
                animationEventTriggered = true;
                receivedAnimation = animName;
            };
            
            // Act - 模拟地面状态改变（通过直接调用移动组件的事件）
            // 注意：这是一个简化的测试，实际游戏中地面状态由物理系统控制
            if (characterMovement.OnGroundedChanged != null)
            {
                characterMovement.OnGroundedChanged.Invoke(true);
            }
            
            yield return null;
            
            // Assert
            Assert.IsTrue(animationEventTriggered, "着陆应该触发动画事件");
            Assert.AreEqual("Landing", receivedAnimation, "应该播放着陆动画");
        }
        
        [Test]
        public void Integration_AnimationEvents_PropagateCorrectly()
        {
            // 测试动画事件是否正确传播到角色控制器
            
            // Arrange
            bool controllerEventTriggered = false;
            bool animatorEventTriggered = false;
            
            characterController.OnAnimationChanged += (animName) => controllerEventTriggered = true;
            characterAnimator.OnAnimationStateChanged += (animName) => animatorEventTriggered = true;
            
            // Act
            characterAnimator.PlayWalking();
            
            // Assert
            Assert.IsTrue(animatorEventTriggered, "动画器事件应该触发");
            Assert.IsTrue(controllerEventTriggered, "控制器事件应该触发");
        }
        
        [Test]
        public void Integration_SetAnimation_WorksThroughController()
        {
            // 测试通过控制器设置动画是否正常工作
            
            // Arrange
            bool animationEventTriggered = false;
            string receivedAnimation = "";
            
            characterController.OnAnimationChanged += (animName) => 
            {
                animationEventTriggered = true;
                receivedAnimation = animName;
            };
            
            // Act
            characterController.SetAnimation("CustomAnimation");
            
            // Assert
            Assert.IsTrue(animationEventTriggered, "设置动画应该触发事件");
            Assert.AreEqual("CustomAnimation", receivedAnimation, "应该接收到正确的动画名称");
        }
        
        [UnityTest]
        public IEnumerator Integration_ContinuousMovement_UpdatesAnimationContinuously()
        {
            // 测试持续移动时动画参数的连续更新
            
            // Arrange
            Vector2 moveDirection = new Vector2(1f, 0f);
            
            // Act - 开始移动
            characterController.Move(moveDirection);
            
            // 等待几帧
            for (int i = 0; i < 5; i++)
            {
                yield return new WaitForFixedUpdate();
                
                // 继续移动以保持动画更新
                characterController.Move(moveDirection);
            }
            
            // Assert
            // 验证动画系统在持续移动期间正常工作
            Assert.DoesNotThrow(() => characterController.Move(moveDirection), 
                "持续移动不应该导致异常");
        }
        
        [Test]
        public void Integration_MultipleAnimationCommands_HandleCorrectly()
        {
            // 测试快速连续的动画命令是否被正确处理
            
            // Arrange
            int animationEventCount = 0;
            characterController.OnAnimationChanged += (animName) => animationEventCount++;
            
            // Act - 快速连续执行多个动画命令
            characterController.SetIdleState();
            characterController.PerformAction();
            characterController.Jump();
            characterController.SetIdleState();
            
            // Assert
            Assert.Greater(animationEventCount, 0, "应该触发多个动画事件");
            Assert.LessOrEqual(animationEventCount, 4, "事件数量应该合理");
        }
        
        [Test]
        public void Integration_CharacterDataUpdate_UpdatesAnimationSystem()
        {
            // 测试角色数据更新是否影响动画系统
            
            // Arrange
            CharacterData newData = new CharacterData
            {
                moveSpeed = 8f,
                jumpForce = 15f,
                maxHealth = 150,
                currentHealth = 150
            };
            
            // Act
            characterController.SetCharacterData(newData);
            
            // Assert
            Assert.DoesNotThrow(() => characterController.Move(Vector2.right), 
                "更新角色数据后移动应该正常工作");
            Assert.DoesNotThrow(() => characterController.Jump(), 
                "更新角色数据后跳跃应该正常工作");
        }
        
        #region Requirements Validation
        
        [Test]
        public void Requirements_AnimatorControllerIntegration_WorksCorrectly()
        {
            // 验证需求2.4: Animator Controller管理角色状态
            
            // 验证所有基础状态都能正确触发
            Assert.DoesNotThrow(() => characterController.SetIdleState(), 
                "待机状态应该正常工作");
            Assert.DoesNotThrow(() => characterController.Move(Vector2.right), 
                "行走状态应该正常工作");
            Assert.DoesNotThrow(() => characterController.Jump(), 
                "跳跃状态应该正常工作");
            Assert.DoesNotThrow(() => characterController.PerformAction(), 
                "动作状态应该正常工作");
        }
        
        [Test]
        public void Requirements_SpriteFlippingIntegration_WorksCorrectly()
        {
            // 验证需求2.5: 角色精灵翻转逻辑集成
            
            // 测试通过移动触发精灵翻转
            bool facingRight = characterAnimator.FacingRight;
            
            // 这里我们直接测试动画器的翻转功能，因为移动触发的翻转需要物理更新
            characterAnimator.SetFacingDirection(false);
            Assert.IsFalse(characterAnimator.FacingRight, "精灵翻转应该正常工作");
            Assert.IsTrue(spriteRenderer.flipX, "SpriteRenderer应该被翻转");
            
            characterAnimator.SetFacingDirection(true);
            Assert.IsTrue(characterAnimator.FacingRight, "精灵翻转应该能恢复");
            Assert.IsFalse(spriteRenderer.flipX, "SpriteRenderer翻转应该能恢复");
        }
        
        [Test]
        public void Requirements_AnimationStateTransitionsIntegration_WorksCorrectly()
        {
            // 验证需求2.6: 动画状态转换和触发器集成
            
            // 测试状态转换通过控制器命令正确触发
            bool animationChanged = false;
            string lastAnimation = "";
            
            characterController.OnAnimationChanged += (animName) => 
            {
                animationChanged = true;
                lastAnimation = animName;
            };
            
            // 测试各种状态转换
            characterController.SetIdleState();
            Assert.IsTrue(animationChanged && lastAnimation == "Idle", "待机转换应该正常");
            
            animationChanged = false;
            characterController.PerformAction();
            Assert.IsTrue(animationChanged && lastAnimation == "Action", "动作转换应该正常");
            
            animationChanged = false;
            characterController.Jump();
            Assert.IsTrue(animationChanged && lastAnimation == "Jumping", "跳跃转换应该正常");
        }
        
        #endregion
    }
}
using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using System.Collections;
using MobileScrollingGame.Player;
using MobileScrollingGame.Input;
using MobileScrollingGame.Core;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 角色移动系统集成测试
    /// 测试角色控制器与输入系统的集成
    /// </summary>
    public class CharacterMovementIntegrationTests
    {
        private GameObject testCharacter;
        private MobileScrollingGame.Player.CharacterController characterController;
        private GameObject inputObject;
        private InputMapper inputMapper;
        
        [SetUp]
        public void SetUp()
        {
            // 创建测试角色
            testCharacter = new GameObject("TestCharacter");
            testCharacter.AddComponent<Rigidbody2D>();
            testCharacter.AddComponent<BoxCollider2D>();
            characterController = testCharacter.AddComponent<MobileScrollingGame.Player.CharacterController>();
            
            // 创建输入系统
            inputObject = new GameObject("InputSystem");
            inputObject.AddComponent<TouchInputHandler>();
            inputObject.AddComponent<VirtualControls>();
            inputMapper = inputObject.AddComponent<InputMapper>();
            
            // 等待组件初始化
            // Unity会自动调用Awake方法
        }
        
        [TearDown]
        public void TearDown()
        {
            if (testCharacter != null)
            {
                Object.DestroyImmediate(testCharacter);
            }
            if (inputObject != null)
            {
                Object.DestroyImmediate(inputObject);
            }
        }
        
        [Test]
        public void Integration_CharacterControllerWithInputMapper_ShouldWork()
        {
            // Arrange
            IInputHandler inputHandler = inputMapper;
            ICharacterController charController = characterController;
            
            // Act & Assert - 不应该抛出异常
            Assert.DoesNotThrow(() => 
            {
                Vector2 moveInput = inputHandler.GetMovementInput();
                charController.Move(moveInput);
                
                bool jumpInput = inputHandler.GetJumpInput();
                if (jumpInput)
                {
                    charController.Jump();
                }
                
                bool actionInput = inputHandler.GetActionInput();
                if (actionInput)
                {
                    charController.PerformAction();
                }
            }, "角色控制器应该能够处理输入系统的输入");
        }
        
        [UnityTest]
        public IEnumerator Integration_MovementInputToCharacter_ShouldMoveCharacter()
        {
            // Arrange
            Vector3 initialPosition = testCharacter.transform.position;
            
            // Act - 模拟右移输入
            characterController.Move(new Vector2(1f, 0f));
            
            // 等待几个物理帧
            yield return new WaitForFixedUpdate();
            yield return new WaitForFixedUpdate();
            yield return new WaitForFixedUpdate();
            
            // Assert
            Rigidbody2D rb = testCharacter.GetComponent<Rigidbody2D>();
            Assert.Greater(Mathf.Abs(rb.linearVelocity.x), 0f, "角色应该有水平速度");
        }
        
        [Test]
        public void Integration_CharacterHealthSystem_ShouldWork()
        {
            // Arrange
            int initialHealth = characterController.GetHealth();
            bool healthChangedEventFired = false;
            bool deathEventFired = false;
            
            characterController.OnHealthChanged += (health) => healthChangedEventFired = true;
            characterController.OnDeath += () => deathEventFired = true;
            
            // Act - 造成伤害
            characterController.TakeDamage(20);
            
            // Assert
            Assert.Less(characterController.GetHealth(), initialHealth, "生命值应该减少");
            Assert.IsTrue(healthChangedEventFired, "生命值改变事件应该触发");
            
            // Act - 造成致命伤害
            characterController.TakeDamage(characterController.GetHealth() + 10);
            
            // Assert
            Assert.AreEqual(0, characterController.GetHealth(), "生命值应该为0");
            Assert.IsTrue(deathEventFired, "死亡事件应该触发");
        }
        
        [Test]
        public void Integration_CharacterAnimationEvents_ShouldWork()
        {
            // Arrange
            bool animationEventFired = false;
            string receivedAnimation = "";
            
            characterController.OnAnimationChanged += (animName) => 
            {
                animationEventFired = true;
                receivedAnimation = animName;
            };
            
            // Act - 测试各种动画触发
            characterController.SetAnimation("TestAnimation");
            Assert.IsTrue(animationEventFired, "动画事件应该触发");
            Assert.AreEqual("TestAnimation", receivedAnimation, "应该接收到正确的动画名称");
            
            // Reset
            animationEventFired = false;
            receivedAnimation = "";
            
            // Act - 测试跳跃动画
            characterController.Jump();
            // 注意：由于角色可能不在地面上，跳跃可能不会成功，但不应该抛出异常
            Assert.DoesNotThrow(() => characterController.Jump(), "跳跃方法不应该抛出异常");
        }
        
        [Test]
        public void Integration_RequirementValidation_BasicMovement()
        {
            // 验证需求2.1: 角色移动时，摄像机应该水平跟随角色
            // 这里我们验证角色位置可以被获取，为摄像机跟随提供基础
            
            Vector3 position = characterController.GetPosition();
            Assert.IsNotNull(position, "角色位置应该可以获取");
            
            // 验证角色可以移动
            Assert.DoesNotThrow(() => characterController.Move(Vector2.right), 
                "角色应该能够接收移动指令");
        }
        
        [Test]
        public void Integration_RequirementValidation_JumpMechanism()
        {
            // 验证需求2.3: 角色跳跃时，移动应该感觉响应迅速且具有适当的重力
            
            // 验证跳跃功能存在
            Assert.DoesNotThrow(() => characterController.Jump(), 
                "角色应该有跳跃功能");
            
            // 验证地面检测功能
            bool isGrounded = characterController.IsGrounded();
            Assert.IsTrue(isGrounded == true || isGrounded == false, 
                "地面检测应该返回布尔值");
        }
        
        [UnityTest]
        public IEnumerator Integration_RequirementValidation_PhysicsMovement()
        {
            // 验证物理移动系统正常工作
            
            // Arrange
            Rigidbody2D rb = testCharacter.GetComponent<Rigidbody2D>();
            Vector2 initialVelocity = rb.linearVelocity;
            
            // Act
            characterController.Move(new Vector2(1f, 0f));
            yield return new WaitForFixedUpdate();
            yield return new WaitForFixedUpdate();
            
            // Assert
            Assert.AreNotEqual(initialVelocity.x, rb.linearVelocity.x, 
                "角色的物理速度应该因移动输入而改变");
        }
        
        [Test]
        public void Integration_CharacterDataConfiguration_ShouldWork()
        {
            // 验证角色数据配置系统
            
            // Arrange
            CharacterData newData = new CharacterData
            {
                moveSpeed = 8f,
                jumpForce = 15f,
                maxHealth = 150,
                currentHealth = 150
            };
            
            // Act
            characterController.SetCharacterData(newData);
            
            // Assert
            CharacterData retrievedData = characterController.GetCharacterData();
            Assert.AreEqual(8f, retrievedData.moveSpeed, "移动速度应该被正确设置");
            Assert.AreEqual(15f, retrievedData.jumpForce, "跳跃力度应该被正确设置");
            Assert.AreEqual(150, retrievedData.maxHealth, "最大生命值应该被正确设置");
            Assert.AreEqual(150, characterController.GetHealth(), "当前生命值应该被更新");
        }
    }
}
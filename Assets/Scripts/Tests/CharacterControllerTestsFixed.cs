using NUnit.Framework;
using UnityEngine;
using MobileScrollingGame.Player;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// CharacterController的修复版单元测试
    /// 解决了动画状态机转换问题
    /// </summary>
    public class CharacterControllerTestsFixed
    {
        private GameObject testCharacter;
        private MobileScrollingGame.Player.CharacterController characterController;
        private CharacterData testData;
        
        [SetUp]
        public void SetUp()
        {
            // 创建测试角色对象
            testCharacter = new GameObject("TestCharacter");
            
            // 添加必要的组件（但不添加Animator避免动画状态机问题）
            testCharacter.AddComponent<Rigidbody2D>();
            testCharacter.AddComponent<BoxCollider2D>();
            
            // 添加CharacterController组件
            characterController = testCharacter.AddComponent<MobileScrollingGame.Player.CharacterController>();
            
            // 创建测试数据
            testData = new CharacterData
            {
                moveSpeed = 5f,
                jumpForce = 10f,
                maxHealth = 100,
                currentHealth = 100,
                isGrounded = true,
                facingRight = true
            };
            
            // 设置角色数据
            characterController.SetCharacterData(testData);
        }
        
        [TearDown]
        public void TearDown()
        {
            if (testCharacter != null)
                Object.DestroyImmediate(testCharacter);
        }
        
        [Test]
        public void GetHealth_ReturnsCorrectHealth()
        {
            // Act
            int health = characterController.GetHealth();
            
            // Assert
            Assert.AreEqual(testData.currentHealth, health, "应该返回正确的生命值");
        }
        
        [Test]
        public void TakeDamage_ReducesHealth()
        {
            // Arrange
            int initialHealth = characterController.GetHealth();
            int damage = 20;
            
            // Act
            characterController.TakeDamage(damage);
            
            // Assert
            int newHealth = characterController.GetHealth();
            Assert.AreEqual(initialHealth - damage, newHealth, "伤害应该减少生命值");
        }
        
        [Test]
        public void TakeDamage_TriggersHealthChangedEvent()
        {
            // Arrange
            bool healthChangedEventTriggered = false;
            int newHealthValue = 0;
            characterController.OnHealthChanged += (health) => 
            {
                healthChangedEventTriggered = true;
                newHealthValue = health;
            };
            
            int damage = 30;
            int expectedHealth = characterController.GetHealth() - damage;
            
            // Act
            characterController.TakeDamage(damage);
            
            // Assert
            Assert.IsTrue(healthChangedEventTriggered, "受伤应该触发生命值改变事件");
            Assert.AreEqual(expectedHealth, newHealthValue, "事件应该传递正确的生命值");
        }
        
        [Test]
        public void TakeDamage_LethalDamage_TriggersDeathEvent()
        {
            // Arrange
            bool deathEventTriggered = false;
            characterController.OnDeath += () => deathEventTriggered = true;
            int lethalDamage = characterController.GetHealth() + 10;
            
            // Act
            characterController.TakeDamage(lethalDamage);
            
            // Assert
            Assert.IsTrue(deathEventTriggered, "致命伤害应该触发死亡事件");
            Assert.AreEqual(0, characterController.GetHealth(), "致命伤害后生命值应该为0");
        }
        
        [Test]
        public void TakeDamage_HealthCannotGoBelowZero()
        {
            // Arrange
            int excessiveDamage = characterController.GetHealth() + 50;
            
            // Act
            characterController.TakeDamage(excessiveDamage);
            
            // Assert
            Assert.AreEqual(0, characterController.GetHealth(), "生命值不应该低于0");
        }
        
        [Test]
        public void Move_UpdatesPosition()
        {
            // Arrange
            Vector2 moveDirection = Vector2.right;
            Vector3 initialPosition = testCharacter.transform.position;
            
            // Act
            characterController.Move(moveDirection);
            
            // Assert - 由于没有实际的物理更新，这里主要测试方法调用不会出错
            Assert.DoesNotThrow(() => characterController.Move(moveDirection), 
                "Move方法应该能够被调用而不抛出异常");
        }
        
        [Test]
        public void Jump_DoesNotThrowException()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => characterController.Jump(), 
                "Jump方法应该能够被调用而不抛出异常");
        }
        
        [Test]
        public void SetAnimation_TriggersAnimationChangedEvent()
        {
            // Arrange
            bool animationChangedEventTriggered = false;
            string receivedAnimationName = "";
            characterController.OnAnimationChanged += (animName) => 
            {
                animationChangedEventTriggered = true;
                receivedAnimationName = animName;
            };
            
            string testAnimationName = "TestAnimation";
            
            // Act
            characterController.SetAnimation(testAnimationName);
            
            // Assert
            Assert.IsTrue(animationChangedEventTriggered, "设置动画应该触发动画改变事件");
            Assert.AreEqual(testAnimationName, receivedAnimationName, "事件应该传递正确的动画名称");
        }
        
        [Test]
        public void SetIdleState_CallsIdleSystem()
        {
            // Act & Assert - 不应该抛出异常
            Assert.DoesNotThrow(() => characterController.SetIdleState(), 
                "SetIdleState方法应该能够被调用");
        }
        
        [Test]
        public void SetCharacterData_UpdatesCharacterProperties()
        {
            // Arrange
            var newData = new CharacterData
            {
                moveSpeed = 8f,
                jumpForce = 15f,
                maxHealth = 150,
                currentHealth = 120,
                isGrounded = false,
                facingRight = false
            };
            
            // Act
            characterController.SetCharacterData(newData);
            
            // Assert
            Assert.AreEqual(newData.currentHealth, characterController.GetHealth(), 
                "设置角色数据应该更新生命值");
        }
        
        [Test]
        public void ComponentIntegrity_AllComponentsExist()
        {
            // Assert
            Assert.IsNotNull(testCharacter, "测试角色对象应该存在");
            Assert.IsNotNull(characterController, "CharacterController组件应该存在");
            
            // 验证必要的物理组件
            Assert.IsNotNull(testCharacter.GetComponent<Rigidbody2D>(), "Rigidbody2D组件应该存在");
            Assert.IsNotNull(testCharacter.GetComponent<BoxCollider2D>(), "BoxCollider2D组件应该存在");
        }
        
        [Test]
        public void EdgeCase_ZeroDamage_DoesNotChangeHealth()
        {
            // Arrange
            int initialHealth = characterController.GetHealth();
            
            // Act
            characterController.TakeDamage(0);
            
            // Assert
            Assert.AreEqual(initialHealth, characterController.GetHealth(), 
                "零伤害不应该改变生命值");
        }
        
        [Test]
        public void EdgeCase_NegativeDamage_DoesNotIncreaseHealth()
        {
            // Arrange
            int initialHealth = characterController.GetHealth();
            
            // Act
            characterController.TakeDamage(-10);
            
            // Assert
            Assert.AreEqual(initialHealth, characterController.GetHealth(), 
                "负伤害不应该增加生命值");
        }
        
        [Test]
        public void EdgeCase_MultipleDeathEvents_OnlyTriggersOnce()
        {
            // Arrange
            int deathEventCount = 0;
            characterController.OnDeath += () => deathEventCount++;
            
            int lethalDamage = characterController.GetHealth() + 10;
            
            // Act - 造成致命伤害两次
            characterController.TakeDamage(lethalDamage);
            characterController.TakeDamage(lethalDamage);
            
            // Assert
            Assert.AreEqual(1, deathEventCount, "死亡事件应该只触发一次");
        }
        
        [Test]
        public void PublicMethods_DoNotThrowExceptions()
        {
            // Test all public methods don't throw exceptions
            Assert.DoesNotThrow(() => characterController.GetHealth());
            Assert.DoesNotThrow(() => characterController.TakeDamage(10));
            Assert.DoesNotThrow(() => characterController.Move(Vector2.zero));
            Assert.DoesNotThrow(() => characterController.Jump());
            Assert.DoesNotThrow(() => characterController.SetAnimation("Test"));
            Assert.DoesNotThrow(() => characterController.SetIdleState());
        }
        
        [Test]
        public void EventSubscription_CanSubscribeAndUnsubscribe()
        {
            // Arrange
            System.Action<int> healthHandler = (health) => { /* 测试处理器 */ };
            System.Action deathHandler = () => { /* 测试处理器 */ };
            System.Action<string> animHandler = (anim) => { /* 测试处理器 */ };
            
            // Act - Subscribe
            Assert.DoesNotThrow(() => 
            {
                characterController.OnHealthChanged += healthHandler;
                characterController.OnDeath += deathHandler;
                characterController.OnAnimationChanged += animHandler;
            }, "事件订阅不应该抛出异常");
            
            // Act - Unsubscribe
            Assert.DoesNotThrow(() => 
            {
                characterController.OnHealthChanged -= healthHandler;
                characterController.OnDeath -= deathHandler;
                characterController.OnAnimationChanged -= animHandler;
            }, "事件取消订阅不应该抛出异常");
        }
    }
}
using UnityEngine;
using UnityEngine.TestTools;
using NUnit.Framework;
using System.Collections;
using MobileScrollingGame.Player;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 碰撞检测器测试类
    /// 测试地面检测、墙壁检测、障碍物检测和边界检测功能
    /// </summary>
    public class CollisionDetectorTests
    {
        private GameObject testCharacter;
        private CollisionDetector collisionDetector;
        private BoxCollider2D characterCollider;
        private Rigidbody2D characterRigidbody;
        
        // 测试环境对象
        private GameObject groundObject;
        private GameObject wallObject;
        private GameObject obstacleObject;
        private GameObject platformObject;
        
        [SetUp]
        public void SetUp()
        {
            // 创建测试角色
            testCharacter = new GameObject("TestCharacter");
            characterCollider = testCharacter.AddComponent<BoxCollider2D>();
            characterRigidbody = testCharacter.AddComponent<Rigidbody2D>();
            collisionDetector = testCharacter.AddComponent<CollisionDetector>();
            
            // 设置角色碰撞器
            characterCollider.size = new Vector2(1f, 2f);
            characterRigidbody.gravityScale = 1f;
            
            // 创建测试环境
            CreateTestEnvironment();
            
            // 设置碰撞检测器参数
            SetupCollisionDetector();
        }
        
        [TearDown]
        public void TearDown()
        {
            // 清理测试对象
            if (testCharacter != null)
                Object.DestroyImmediate(testCharacter);
            if (groundObject != null)
                Object.DestroyImmediate(groundObject);
            if (wallObject != null)
                Object.DestroyImmediate(wallObject);
            if (obstacleObject != null)
                Object.DestroyImmediate(obstacleObject);
            if (platformObject != null)
                Object.DestroyImmediate(platformObject);
        }
        
        /// <summary>
        /// 创建测试环境
        /// </summary>
        private void CreateTestEnvironment()
        {
            // 创建地面对象
            groundObject = new GameObject("Ground");
            groundObject.layer = 0; // Default layer
            var groundCollider = groundObject.AddComponent<BoxCollider2D>();
            groundCollider.size = new Vector2(10f, 1f);
            groundObject.transform.position = new Vector3(0f, -2f, 0f);
            
            // 创建墙壁对象
            wallObject = new GameObject("Wall");
            wallObject.layer = 9; // Wall layer
            var wallCollider = wallObject.AddComponent<BoxCollider2D>();
            wallCollider.size = new Vector2(1f, 5f);
            wallObject.transform.position = new Vector3(3f, 0f, 0f);
            
            // 创建障碍物对象
            obstacleObject = new GameObject("Obstacle");
            obstacleObject.layer = 10; // Obstacle layer
            var obstacleCollider = obstacleObject.AddComponent<BoxCollider2D>();
            obstacleCollider.size = new Vector2(1f, 1f);
            obstacleObject.transform.position = new Vector3(1f, 0f, 0f);
            
            // 创建平台对象
            platformObject = new GameObject("Platform");
            platformObject.layer = 8; // Platform layer
            var platformCollider = platformObject.AddComponent<BoxCollider2D>();
            platformCollider.size = new Vector2(3f, 0.5f);
            platformObject.transform.position = new Vector3(0f, 1f, 0f);
        }
        
        /// <summary>
        /// 设置碰撞检测器参数
        /// </summary>
        private void SetupCollisionDetector()
        {
            LayerMask groundMask = 1; // Default layer
            LayerMask platformMask = 1 << 8; // Platform layer
            LayerMask wallMask = 1 << 9; // Wall layer
            LayerMask obstacleMask = 1 << 10; // Obstacle layer
            
            collisionDetector.SetLayerMasks(groundMask, platformMask, wallMask, obstacleMask);
            collisionDetector.SetDetectionParameters(0.2f, 0.15f, 0.3f);
            collisionDetector.SetBoundaryCheck(true, new Vector2(50f, 30f));
        }
        
        #region 地面检测测试
        
        [Test]
        public void GroundDetection_WhenCharacterOnGround_ShouldReturnTrue()
        {
            // Arrange
            testCharacter.transform.position = new Vector3(0f, -1f, 0f);
            
            // Act
            collisionDetector.ForceUpdateDetection();
            
            // Assert
            Assert.IsTrue(collisionDetector.IsGrounded, "角色在地面上时应该检测到地面");
        }
        
        [Test]
        public void GroundDetection_WhenCharacterInAir_ShouldReturnFalse()
        {
            // Arrange
            testCharacter.transform.position = new Vector3(0f, 2f, 0f);
            
            // Act
            collisionDetector.ForceUpdateDetection();
            
            // Assert
            Assert.IsFalse(collisionDetector.IsGrounded, "角色在空中时不应该检测到地面");
        }
        
        [Test]
        public void GroundDetection_GetGroundNormal_ShouldReturnCorrectNormal()
        {
            // Arrange
            testCharacter.transform.position = new Vector3(0f, -1f, 0f);
            
            // Act
            collisionDetector.ForceUpdateDetection();
            Vector2 normal = collisionDetector.GetGroundNormal();
            
            // Assert
            Assert.AreEqual(Vector2.up, normal, "地面法线应该向上");
        }
        
        [Test]
        public void GroundDetection_GetGroundAngle_ShouldReturnZeroForFlatGround()
        {
            // Arrange
            testCharacter.transform.position = new Vector3(0f, -1f, 0f);
            
            // Act
            collisionDetector.ForceUpdateDetection();
            float angle = collisionDetector.GetGroundAngle();
            
            // Assert
            Assert.AreEqual(0f, angle, 0.1f, "平坦地面的角度应该为0");
        }
        
        [Test]
        public void GroundDetection_GetClosestGroundPoint_ShouldReturnCorrectPoint()
        {
            // Arrange
            testCharacter.transform.position = new Vector3(0f, -1f, 0f);
            
            // Act
            collisionDetector.ForceUpdateDetection();
            Vector2 groundPoint = collisionDetector.GetClosestGroundPoint();
            
            // Assert
            Assert.AreEqual(-1.5f, groundPoint.y, 0.1f, "最近地面点的Y坐标应该正确");
        }
        
        #endregion
        
        #region 墙壁检测测试
        
        [Test]
        public void WallDetection_WhenCharacterNearWall_ShouldReturnTrue()
        {
            // Arrange
            testCharacter.transform.position = new Vector3(2.4f, 0f, 0f); // 靠近墙壁
            
            // Act
            collisionDetector.ForceUpdateDetection();
            
            // Assert
            Assert.IsTrue(collisionDetector.IsAgainstWall, "角色靠近墙壁时应该检测到墙壁");
        }
        
        [Test]
        public void WallDetection_WhenCharacterAwayFromWall_ShouldReturnFalse()
        {
            // Arrange
            testCharacter.transform.position = new Vector3(0f, 0f, 0f); // 远离墙壁
            
            // Act
            collisionDetector.ForceUpdateDetection();
            
            // Assert
            Assert.IsFalse(collisionDetector.IsAgainstWall, "角色远离墙壁时不应该检测到墙壁");
        }
        
        [Test]
        public void WallDetection_CanMoveInDirection_ShouldBlockMovementTowardsWall()
        {
            // Arrange
            testCharacter.transform.position = new Vector3(2.4f, 0f, 0f); // 靠近右侧墙壁
            
            // Act
            collisionDetector.ForceUpdateDetection();
            bool canMoveRight = collisionDetector.CanMoveInDirection(Vector2.right);
            bool canMoveLeft = collisionDetector.CanMoveInDirection(Vector2.left);
            
            // Assert
            Assert.IsFalse(canMoveRight, "不应该能向墙壁方向移动");
            Assert.IsTrue(canMoveLeft, "应该能向远离墙壁方向移动");
        }
        
        #endregion
        
        #region 障碍物检测测试
        
        [Test]
        public void ObstacleDetection_WhenCharacterNearObstacle_ShouldReturnTrue()
        {
            // Arrange
            testCharacter.transform.position = new Vector3(1.2f, 0f, 0f); // 靠近障碍物
            
            // Act
            collisionDetector.ForceUpdateDetection();
            
            // Assert
            Assert.IsTrue(collisionDetector.IsNearObstacle, "角色靠近障碍物时应该检测到障碍物");
        }
        
        [Test]
        public void ObstacleDetection_WhenCharacterAwayFromObstacle_ShouldReturnFalse()
        {
            // Arrange
            testCharacter.transform.position = new Vector3(-2f, 0f, 0f); // 远离障碍物
            
            // Act
            collisionDetector.ForceUpdateDetection();
            
            // Assert
            Assert.IsFalse(collisionDetector.IsNearObstacle, "角色远离障碍物时不应该检测到障碍物");
        }
        
        [Test]
        public void ObstacleDetection_CanMoveInDirection_ShouldBlockMovementWhenNearObstacle()
        {
            // Arrange
            testCharacter.transform.position = new Vector3(1.2f, 0f, 0f); // 靠近障碍物
            
            // Act
            collisionDetector.ForceUpdateDetection();
            bool canMove = collisionDetector.CanMoveInDirection(Vector2.right);
            
            // Assert
            Assert.IsFalse(canMove, "靠近障碍物时不应该能移动");
        }
        
        #endregion
        
        #region 平台检测测试
        
        [Test]
        public void PlatformDetection_WhenCharacterOnPlatform_ShouldReturnTrue()
        {
            // Arrange
            testCharacter.transform.position = new Vector3(0f, 1.8f, 0f); // 在平台上
            
            // Act
            collisionDetector.ForceUpdateDetection();
            
            // Assert
            Assert.IsTrue(collisionDetector.IsOnPlatform, "角色在平台上时应该检测到平台");
        }
        
        [Test]
        public void PlatformDetection_WhenCharacterNotOnPlatform_ShouldReturnFalse()
        {
            // Arrange
            testCharacter.transform.position = new Vector3(5f, 1.8f, 0f); // 不在平台上
            
            // Act
            collisionDetector.ForceUpdateDetection();
            
            // Assert
            Assert.IsFalse(collisionDetector.IsOnPlatform, "角色不在平台上时不应该检测到平台");
        }
        
        #endregion
        
        #region 边界检测测试
        
        [Test]
        public void BoundaryDetection_WhenCharacterWithinBounds_ShouldReturnFalse()
        {
            // Arrange
            testCharacter.transform.position = new Vector3(0f, 0f, 0f); // 在边界内
            
            // Act
            collisionDetector.ForceUpdateDetection();
            
            // Assert
            Assert.IsFalse(collisionDetector.IsOutOfBounds, "角色在边界内时不应该超出边界");
        }
        
        [Test]
        public void BoundaryDetection_WhenCharacterOutOfBounds_ShouldReturnTrue()
        {
            // Arrange
            testCharacter.transform.position = new Vector3(60f, 0f, 0f); // 超出边界
            
            // Act
            collisionDetector.ForceUpdateDetection();
            
            // Assert
            Assert.IsTrue(collisionDetector.IsOutOfBounds, "角色超出边界时应该检测到");
        }
        
        [Test]
        public void BoundaryDetection_SetBoundaryCheck_ShouldDisableWhenSetToFalse()
        {
            // Arrange
            testCharacter.transform.position = new Vector3(60f, 0f, 0f); // 超出边界
            collisionDetector.SetBoundaryCheck(false);
            
            // Act
            collisionDetector.ForceUpdateDetection();
            
            // Assert
            Assert.IsFalse(collisionDetector.IsOutOfBounds, "禁用边界检测时不应该检测到超出边界");
        }
        
        #endregion
        
        #region 参数设置测试
        
        [Test]
        public void SetDetectionParameters_ShouldUpdateParameters()
        {
            // Arrange & Act
            collisionDetector.SetDetectionParameters(0.5f, 0.3f, 0.4f);
            
            // 通过改变角色位置来测试参数是否生效
            testCharacter.transform.position = new Vector3(0f, -1.3f, 0f); // 稍微远离地面
            collisionDetector.ForceUpdateDetection();
            
            // Assert
            Assert.IsTrue(collisionDetector.IsGrounded, "更新检测参数后应该能检测到更远的地面");
        }
        
        [Test]
        public void SetLayerMasks_ShouldUpdateLayerMasks()
        {
            // Arrange
            LayerMask newGroundMask = 1 << 15; // 使用不同的图层
            
            // Act
            collisionDetector.SetLayerMasks(newGroundMask, 1 << 8, 1 << 9, 1 << 10);
            testCharacter.transform.position = new Vector3(0f, -1f, 0f);
            collisionDetector.ForceUpdateDetection();
            
            // Assert
            Assert.IsFalse(collisionDetector.IsGrounded, "更新图层遮罩后不应该检测到不同图层的地面");
        }
        
        #endregion
        
        #region 事件测试
        
        [UnityTest]
        public IEnumerator GroundedChangedEvent_ShouldTriggerWhenGroundedStateChanges()
        {
            // Arrange
            bool eventTriggered = false;
            bool groundedState = false;
            
            collisionDetector.OnGroundedChanged += (grounded) =>
            {
                eventTriggered = true;
                groundedState = grounded;
            };
            
            // Act - 将角色移动到地面上
            testCharacter.transform.position = new Vector3(0f, -1f, 0f);
            
            yield return new WaitForFixedUpdate();
            
            // Assert
            Assert.IsTrue(eventTriggered, "地面状态改变时应该触发事件");
            Assert.IsTrue(groundedState, "事件应该传递正确的地面状态");
        }
        
        [UnityTest]
        public IEnumerator WallCollisionChangedEvent_ShouldTriggerWhenWallStateChanges()
        {
            // Arrange
            bool eventTriggered = false;
            bool wallState = false;
            
            collisionDetector.OnWallCollisionChanged += (againstWall) =>
            {
                eventTriggered = true;
                wallState = againstWall;
            };
            
            // Act - 将角色移动到墙壁附近
            testCharacter.transform.position = new Vector3(2.4f, 0f, 0f);
            
            yield return new WaitForFixedUpdate();
            
            // Assert
            Assert.IsTrue(eventTriggered, "墙壁碰撞状态改变时应该触发事件");
            Assert.IsTrue(wallState, "事件应该传递正确的墙壁状态");
        }
        
        [UnityTest]
        public IEnumerator ObstacleDetectedEvent_ShouldTriggerWhenObstacleStateChanges()
        {
            // Arrange
            bool eventTriggered = false;
            bool obstacleState = false;
            
            collisionDetector.OnObstacleDetected += (nearObstacle) =>
            {
                eventTriggered = true;
                obstacleState = nearObstacle;
            };
            
            // Act - 将角色移动到障碍物附近
            testCharacter.transform.position = new Vector3(1.2f, 0f, 0f);
            
            yield return new WaitForFixedUpdate();
            
            // Assert
            Assert.IsTrue(eventTriggered, "障碍物检测状态改变时应该触发事件");
            Assert.IsTrue(obstacleState, "事件应该传递正确的障碍物状态");
        }
        
        [UnityTest]
        public IEnumerator BoundaryChangedEvent_ShouldTriggerWhenBoundaryStateChanges()
        {
            // Arrange
            bool eventTriggered = false;
            bool boundaryState = false;
            
            collisionDetector.OnBoundaryChanged += (outOfBounds) =>
            {
                eventTriggered = true;
                boundaryState = outOfBounds;
            };
            
            // Act - 将角色移动到边界外
            testCharacter.transform.position = new Vector3(60f, 0f, 0f);
            
            yield return new WaitForFixedUpdate();
            
            // Assert
            Assert.IsTrue(eventTriggered, "边界状态改变时应该触发事件");
            Assert.IsTrue(boundaryState, "事件应该传递正确的边界状态");
        }
        
        #endregion
        
        #region 集成测试
        
        [UnityTest]
        public IEnumerator ComplexScenario_CharacterMovingThroughEnvironment_ShouldDetectAllCollisions()
        {
            // Arrange - 角色从空中开始
            testCharacter.transform.position = new Vector3(-3f, 3f, 0f);
            
            bool groundedEventTriggered = false;
            bool wallEventTriggered = false;
            bool obstacleEventTriggered = false;
            
            collisionDetector.OnGroundedChanged += (grounded) => { if (grounded) groundedEventTriggered = true; };
            collisionDetector.OnWallCollisionChanged += (wall) => { if (wall) wallEventTriggered = true; };
            collisionDetector.OnObstacleDetected += (obstacle) => { if (obstacle) obstacleEventTriggered = true; };
            
            // Act & Assert - 模拟角色移动过程
            
            // 1. 角色落地
            testCharacter.transform.position = new Vector3(-3f, -1f, 0f);
            yield return new WaitForFixedUpdate();
            Assert.IsTrue(collisionDetector.IsGrounded, "角色应该检测到地面");
            Assert.IsTrue(groundedEventTriggered, "应该触发地面事件");
            
            // 2. 角色移动到障碍物附近
            testCharacter.transform.position = new Vector3(1.2f, -1f, 0f);
            yield return new WaitForFixedUpdate();
            Assert.IsTrue(collisionDetector.IsNearObstacle, "角色应该检测到障碍物");
            Assert.IsTrue(obstacleEventTriggered, "应该触发障碍物事件");
            
            // 3. 角色移动到墙壁附近
            testCharacter.transform.position = new Vector3(2.4f, -1f, 0f);
            yield return new WaitForFixedUpdate();
            Assert.IsTrue(collisionDetector.IsAgainstWall, "角色应该检测到墙壁");
            Assert.IsTrue(wallEventTriggered, "应该触发墙壁事件");
            
            // 4. 验证所有检测同时工作
            Assert.IsTrue(collisionDetector.IsGrounded, "角色仍应该在地面上");
            Assert.IsTrue(collisionDetector.IsAgainstWall, "角色仍应该靠近墙壁");
        }
        
        #endregion
    }
}
using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using System.Collections;
using MobileScrollingGame.Player;
using AnimationState = MobileScrollingGame.Player.AnimationState;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 角色动画系统的单元测试
    /// 测试动画状态管理、转换和精灵翻转功能
    /// </summary>
    public class CharacterAnimationTests
    {
        private GameObject testCharacter;
        private CharacterAnimator characterAnimator;
        private AnimationStateMachine stateMachine;
        private Animator animator;
        private SpriteRenderer spriteRenderer;
        
        [SetUp]
        public void SetUp()
        {
            // 创建测试角色对象
            testCharacter = new GameObject("TestCharacter");
            
            // 添加必要的组件
            animator = testCharacter.AddComponent<Animator>();
            spriteRenderer = testCharacter.AddComponent<SpriteRenderer>();
            characterAnimator = testCharacter.AddComponent<CharacterAnimator>();
            
            // 创建状态机
            stateMachine = new AnimationStateMachine();
            stateMachine.Initialize();
        }
        
        [TearDown]
        public void TearDown()
        {
            if (testCharacter != null)
            {
                Object.DestroyImmediate(testCharacter);
            }
        }
        
        #region CharacterAnimator Tests
        
        [Test]
        public void CharacterAnimator_InitialState_ShouldBeIdle()
        {
            // Act & Assert
            Assert.IsNotNull(characterAnimator, "CharacterAnimator组件应该存在");
            Assert.IsTrue(characterAnimator.FacingRight, "角色初始应该面向右侧");
        }
        
        [Test]
        public void CharacterAnimator_PlayIdle_SetsCorrectState()
        {
            // Act
            characterAnimator.PlayIdle();
            
            // Assert
            Assert.AreEqual("Idle", characterAnimator.CurrentAnimationState, 
                "播放待机动画应该设置正确的状态");
        }
        
        [Test]
        public void CharacterAnimator_PlayWalking_SetsCorrectState()
        {
            // Act
            characterAnimator.PlayWalking();
            
            // Assert
            Assert.AreEqual("Walking", characterAnimator.CurrentAnimationState, 
                "播放行走动画应该设置正确的状态");
        }
        
        [Test]
        public void CharacterAnimator_PlayJumping_SetsCorrectState()
        {
            // Act
            characterAnimator.PlayJumping();
            
            // Assert
            Assert.AreEqual("Jumping", characterAnimator.CurrentAnimationState, 
                "播放跳跃动画应该设置正确的状态");
        }
        
        [Test]
        public void CharacterAnimator_PlayLanding_SetsCorrectState()
        {
            // Act
            characterAnimator.PlayLanding();
            
            // Assert
            Assert.AreEqual("Landing", characterAnimator.CurrentAnimationState, 
                "播放着陆动画应该设置正确的状态");
        }
        
        [Test]
        public void CharacterAnimator_PlayAction_SetsCorrectState()
        {
            // Act
            characterAnimator.PlayAction();
            
            // Assert
            Assert.AreEqual("Action", characterAnimator.CurrentAnimationState, 
                "播放动作动画应该设置正确的状态");
        }
        
        [Test]
        public void CharacterAnimator_SetFacingDirection_Left_FlipsSprite()
        {
            // Arrange
            bool initialFacingRight = characterAnimator.FacingRight;
            
            // Act
            characterAnimator.SetFacingDirection(false);
            
            // Assert
            Assert.IsFalse(characterAnimator.FacingRight, "设置朝向左侧应该更新FacingRight属性");
            Assert.IsTrue(spriteRenderer.flipX, "朝向左侧时精灵应该被翻转");
        }
        
        [Test]
        public void CharacterAnimator_SetFacingDirection_Right_DoesNotFlipSprite()
        {
            // Act
            characterAnimator.SetFacingDirection(true);
            
            // Assert
            Assert.IsTrue(characterAnimator.FacingRight, "设置朝向右侧应该更新FacingRight属性");
            Assert.IsFalse(spriteRenderer.flipX, "朝向右侧时精灵不应该被翻转");
        }
        
        [Test]
        public void CharacterAnimator_UpdateAnimatorParameters_UpdatesCorrectly()
        {
            // Arrange
            Vector2 testVelocity = new Vector2(5f, 2f);
            bool testGrounded = true;
            
            // Act
            characterAnimator.UpdateAnimatorParameters(testVelocity, testGrounded);
            
            // Assert - 验证方法调用不抛出异常
            Assert.DoesNotThrow(() => 
                characterAnimator.UpdateAnimatorParameters(testVelocity, testGrounded),
                "更新动画参数不应该抛出异常");
        }
        
        [Test]
        public void CharacterAnimator_SetAnimationSpeed_ChangesSpeed()
        {
            // Arrange
            float testSpeed = 0.5f;
            
            // Act
            characterAnimator.SetAnimationSpeed(testSpeed);
            
            // Assert
            Assert.AreEqual(testSpeed, animator.speed, "动画速度应该被正确设置");
        }
        
        [Test]
        public void CharacterAnimator_PauseAnimation_SetsSpeedToZero()
        {
            // Act
            characterAnimator.PauseAnimation();
            
            // Assert
            Assert.AreEqual(0f, animator.speed, "暂停动画应该将速度设置为0");
        }
        
        [Test]
        public void CharacterAnimator_ResumeAnimation_SetsSpeedToOne()
        {
            // Arrange
            characterAnimator.PauseAnimation(); // 先暂停
            
            // Act
            characterAnimator.ResumeAnimation();
            
            // Assert
            Assert.AreEqual(1f, animator.speed, "恢复动画应该将速度设置为1");
        }
        
        [Test]
        public void CharacterAnimator_AnimationStateChanged_TriggersEvent()
        {
            // Arrange
            bool eventTriggered = false;
            string receivedState = "";
            
            characterAnimator.OnAnimationStateChanged += (state) => 
            {
                eventTriggered = true;
                receivedState = state;
            };
            
            // Act
            characterAnimator.PlayWalking();
            
            // Assert
            Assert.IsTrue(eventTriggered, "动画状态改变应该触发事件");
            Assert.AreEqual("Walking", receivedState, "事件应该传递正确的状态名称");
        }
        
        [Test]
        public void CharacterAnimator_FacingDirectionChanged_TriggersEvent()
        {
            // Arrange
            bool eventTriggered = false;
            bool receivedDirection = true;
            
            characterAnimator.OnFacingDirectionChanged += (facingRight) => 
            {
                eventTriggered = true;
                receivedDirection = facingRight;
            };
            
            // Act
            characterAnimator.SetFacingDirection(false);
            
            // Assert
            Assert.IsTrue(eventTriggered, "朝向改变应该触发事件");
            Assert.IsFalse(receivedDirection, "事件应该传递正确的朝向");
        }
        
        #endregion
        
        #region AnimationStateMachine Tests
        
        [Test]
        public void AnimationStateMachine_Initialize_SetsDefaultState()
        {
            // Assert
            Assert.AreEqual(AnimationState.Idle, stateMachine.CurrentState, 
                "状态机初始状态应该是Idle");
        }
        
        [Test]
        public void AnimationStateMachine_TryTransitionTo_ValidTransition_ReturnsTrue()
        {
            // Act
            bool result = stateMachine.TryTransitionTo(AnimationState.Walking);
            
            // Assert
            Assert.IsTrue(result, "从Idle到Walking的转换应该成功");
            Assert.AreEqual(AnimationState.Walking, stateMachine.CurrentState, 
                "当前状态应该更新为Walking");
        }
        
        [Test]
        public void AnimationStateMachine_TryTransitionTo_InvalidTransition_ReturnsFalse()
        {
            // Arrange - 先转换到Jumping状态
            stateMachine.TryTransitionTo(AnimationState.Jumping);
            
            // Act - 尝试从Jumping直接转换到Walking（不被允许）
            bool result = stateMachine.TryTransitionTo(AnimationState.Walking);
            
            // Assert
            Assert.IsFalse(result, "从Jumping到Walking的转换应该失败");
            Assert.AreEqual(AnimationState.Jumping, stateMachine.CurrentState, 
                "当前状态应该保持为Jumping");
        }
        
        [Test]
        public void AnimationStateMachine_TryTransitionTo_SameState_ReturnsFalse()
        {
            // Act
            bool result = stateMachine.TryTransitionTo(AnimationState.Idle);
            
            // Assert
            Assert.IsFalse(result, "转换到相同状态应该返回false");
        }
        
        [Test]
        public void AnimationStateMachine_ForceTransitionTo_IgnoresRules()
        {
            // Arrange
            stateMachine.TryTransitionTo(AnimationState.Jumping);
            
            // Act - 强制转换到不被允许的状态
            stateMachine.ForceTransitionTo(AnimationState.Walking);
            
            // Assert
            Assert.AreEqual(AnimationState.Walking, stateMachine.CurrentState, 
                "强制转换应该忽略转换规则");
        }
        
        [Test]
        public void AnimationStateMachine_StateChanged_TriggersEvent()
        {
            // Arrange
            bool eventTriggered = false;
            AnimationState fromState = AnimationState.Idle;
            AnimationState toState = AnimationState.Idle;
            
            stateMachine.OnStateChanged += (from, to) => 
            {
                eventTriggered = true;
                fromState = from;
                toState = to;
            };
            
            // Act
            stateMachine.TryTransitionTo(AnimationState.Walking);
            
            // Assert
            Assert.IsTrue(eventTriggered, "状态改变应该触发事件");
            Assert.AreEqual(AnimationState.Idle, fromState, "事件应该传递正确的源状态");
            Assert.AreEqual(AnimationState.Walking, toState, "事件应该传递正确的目标状态");
        }
        
        [Test]
        public void AnimationStateMachine_GetStateData_ReturnsCorrectData()
        {
            // Act
            AnimationStateData idleData = stateMachine.GetStateData(AnimationState.Idle);
            
            // Assert
            Assert.IsNotNull(idleData, "应该能获取到状态数据");
            Assert.AreEqual(AnimationState.Idle, idleData.state, "状态数据应该匹配");
        }
        
        [Test]
        public void AnimationStateMachine_CanTransitionTo_ValidTransition_ReturnsTrue()
        {
            // Act
            bool canTransition = stateMachine.CanTransitionTo(AnimationState.Walking);
            
            // Assert
            Assert.IsTrue(canTransition, "应该能够转换到Walking状态");
        }
        
        [Test]
        public void AnimationStateMachine_CanTransitionTo_InvalidTransition_ReturnsFalse()
        {
            // Arrange
            stateMachine.TryTransitionTo(AnimationState.Death);
            
            // Act
            bool canTransition = stateMachine.CanTransitionTo(AnimationState.Walking);
            
            // Assert
            Assert.IsFalse(canTransition, "从Death状态不应该能转换到其他状态");
        }
        
        [Test]
        public void AnimationStateMachine_Reset_ReturnsToIdle()
        {
            // Arrange
            stateMachine.TryTransitionTo(AnimationState.Walking);
            
            // Act
            stateMachine.Reset();
            
            // Assert
            Assert.AreEqual(AnimationState.Idle, stateMachine.CurrentState, 
                "重置后应该返回到Idle状态");
        }
        
        [Test]
        public void AnimationStateMachine_GetAllowedTransitions_ReturnsCorrectList()
        {
            // Act
            var allowedTransitions = stateMachine.GetAllowedTransitions();
            
            // Assert
            Assert.IsNotNull(allowedTransitions, "应该返回允许的转换列表");
            Assert.Contains(AnimationState.Walking, allowedTransitions, 
                "从Idle状态应该能转换到Walking");
            Assert.Contains(AnimationState.Jumping, allowedTransitions, 
                "从Idle状态应该能转换到Jumping");
        }
        
        #endregion
        
        #region Integration Tests
        
        [Test]
        public void Integration_CharacterAnimatorWithStateMachine_ShouldWork()
        {
            // 这个测试验证动画器和状态机能够协同工作
            
            // Arrange
            bool animationEventTriggered = false;
            characterAnimator.OnAnimationStateChanged += (state) => animationEventTriggered = true;
            
            // Act
            characterAnimator.PlayWalking();
            
            // Assert
            Assert.IsTrue(animationEventTriggered, "动画状态改变应该触发事件");
            Assert.AreEqual("Walking", characterAnimator.CurrentAnimationState, 
                "动画状态应该正确更新");
        }
        
        [UnityTest]
        public IEnumerator Integration_AnimationParameterUpdates_ShouldWorkOverTime()
        {
            // Arrange
            Vector2 walkingVelocity = new Vector2(3f, 0f);
            bool isGrounded = true;
            
            // Act
            characterAnimator.UpdateAnimatorParameters(walkingVelocity, isGrounded);
            
            // 等待一帧让动画系统处理
            yield return null;
            
            // Assert
            Assert.DoesNotThrow(() => 
                characterAnimator.UpdateAnimatorParameters(walkingVelocity, isGrounded),
                "动画参数更新应该正常工作");
        }
        
        #endregion
        
        #region Requirements Validation Tests
        
        [Test]
        public void Requirements_AnimatorController_ManagesStates()
        {
            // 验证需求2.4: 创建Animator Controller管理角色状态
            
            // 验证所有必需的动画状态都可以播放
            Assert.DoesNotThrow(() => characterAnimator.PlayIdle(), 
                "应该能播放待机动画");
            Assert.DoesNotThrow(() => characterAnimator.PlayWalking(), 
                "应该能播放行走动画");
            Assert.DoesNotThrow(() => characterAnimator.PlayJumping(), 
                "应该能播放跳跃动画");
            Assert.DoesNotThrow(() => characterAnimator.PlayLanding(), 
                "应该能播放着陆动画");
        }
        
        [Test]
        public void Requirements_SpriteFlipping_WorksCorrectly()
        {
            // 验证需求2.5: 实现角色精灵翻转逻辑
            
            // 测试向右朝向
            characterAnimator.SetFacingDirection(true);
            Assert.IsTrue(characterAnimator.FacingRight, "角色应该面向右侧");
            Assert.IsFalse(spriteRenderer.flipX, "精灵不应该被翻转");
            
            // 测试向左朝向
            characterAnimator.SetFacingDirection(false);
            Assert.IsFalse(characterAnimator.FacingRight, "角色应该面向左侧");
            Assert.IsTrue(spriteRenderer.flipX, "精灵应该被翻转");
        }
        
        [Test]
        public void Requirements_AnimationStateTransitions_WorkCorrectly()
        {
            // 验证需求2.6: 添加动画状态转换和触发器
            
            // 测试状态转换
            Assert.IsTrue(stateMachine.TryTransitionTo(AnimationState.Walking), 
                "应该能从Idle转换到Walking");
            Assert.IsTrue(stateMachine.TryTransitionTo(AnimationState.Jumping), 
                "应该能从Walking转换到Jumping");
            Assert.IsTrue(stateMachine.TryTransitionTo(AnimationState.Landing), 
                "应该能从Jumping转换到Landing");
            
            // 测试无效转换
            stateMachine.ForceTransitionTo(AnimationState.Death);
            Assert.IsFalse(stateMachine.TryTransitionTo(AnimationState.Walking), 
                "不应该能从Death转换到其他状态");
        }
        
        #endregion
    }
}
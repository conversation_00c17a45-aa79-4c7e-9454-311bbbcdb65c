using UnityEngine;
using MobileScrollingGame.Camera;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 摄像机边界调试脚本
    /// 用于诊断边界计算问题
    /// </summary>
    public class CameraBoundsDebug : MonoBehaviour
    {
        public static void DebugBoundsCalculation()
        {
            // 模拟测试环境
            var cameraObj = new GameObject("TestCamera");
            var camera = cameraObj.AddComponent<UnityEngine.Camera>();
            camera.orthographic = true;
            camera.orthographicSize = 5f;
            camera.aspect = 16f / 9f; // 1.777...
            
            var follower = cameraObj.AddComponent<CameraFollower>();
            
            // 测试边界 (-5, -3, 10, 6)
            Rect bounds = new Rect(-5, -3, 10, 6);
            
            // 计算摄像机尺寸
            float cameraHeight = camera.orthographicSize * 2f; // 10
            float cameraWidth = cameraHeight * camera.aspect; // 17.77...
            
            // 计算边界限制
            float halfWidth = cameraWidth * 0.5f; // 8.888...
            float halfHeight = cameraHeight * 0.5f; // 5
            
            float minX = bounds.xMin + halfWidth; // -5 + 8.888 = 3.888
            float maxX = bounds.xMax - halfWidth; // 5 - 8.888 = -3.888
            float minY = bounds.yMin + halfHeight; // -3 + 5 = 2
            float maxY = bounds.yMax - halfHeight; // 3 - 5 = -2
            
            Debug.Log($"边界调试信息:");
            Debug.Log($"摄像机尺寸: {cameraWidth:F2} x {cameraHeight:F2}");
            Debug.Log($"边界: {bounds}");
            Debug.Log($"计算的X范围: [{minX:F2}, {maxX:F2}]");
            Debug.Log($"计算的Y范围: [{minY:F2}, {maxY:F2}]");
            Debug.Log($"问题: minX > maxX = {minX > maxX}, minY > maxY = {minY > maxY}");
            
            Object.DestroyImmediate(cameraObj);
        }
    }
}
using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using System.Collections;
using MobileScrollingGame.Input;
using MobileScrollingGame.Core;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 输入系统单元测试
    /// 测试触控输入处理、虚拟控制和输入映射功能
    /// </summary>
    public class InputSystemTests
    {
        private GameObject testGameObject;
        private TouchInputHandler touchInputHandler;
        private VirtualControls virtualControls;
        private InputMapper inputMapper;
        
        [SetUp]
        public void SetUp()
        {
            // 创建测试游戏对象
            testGameObject = new GameObject("InputTestObject");
            
            // 添加输入组件
            touchInputHandler = testGameObject.AddComponent<TouchInputHandler>();
            virtualControls = testGameObject.AddComponent<VirtualControls>();
            inputMapper = testGameObject.AddComponent<InputMapper>();
        }
        
        [TearDown]
        public void TearDown()
        {
            // 清理测试对象
            if (testGameObject != null)
            {
                Object.DestroyImmediate(testGameObject);
            }
        }
        
        #region TouchInputHandler 测试
        
        [Test]
        public void TouchInputHandler_InitialState_ShouldBeZero()
        {
            // 测试初始状态
            Assert.AreEqual(Vector2.zero, touchInputHandler.GetMovementInput());
            Assert.IsFalse(touchInputHandler.GetJumpInput());
            Assert.IsFalse(touchInputHandler.GetActionInput());
            Assert.IsFalse(touchInputHandler.HasActiveInput());
        }
        
        [Test]
        public void TouchInputHandler_EnableDisableInput_ShouldWork()
        {
            // 测试输入启用/禁用
            touchInputHandler.EnableInput(false);
            Assert.AreEqual(Vector2.zero, touchInputHandler.GetMovementInput());
            Assert.IsFalse(touchInputHandler.HasActiveInput());
            
            touchInputHandler.EnableInput(true);
            // 输入应该可以正常工作（需要实际输入事件来测试）
        }
        
        [Test]
        public void TouchInputHandler_ResetInputs_ShouldClearAllStates()
        {
            // 测试输入重置
            touchInputHandler.ResetInputs();
            
            Assert.AreEqual(Vector2.zero, touchInputHandler.GetMovementInput());
            Assert.IsFalse(touchInputHandler.GetJumpInput());
            Assert.IsFalse(touchInputHandler.GetActionInput());
            Assert.IsFalse(touchInputHandler.HasActiveInput());
            Assert.IsFalse(touchInputHandler.IsMovementTouchActive());
            Assert.AreEqual(-1, touchInputHandler.GetMovementTouchId());
        }
        
        [Test]
        public void TouchInputHandler_DebugMode_ShouldToggle()
        {
            // 测试调试模式切换
            touchInputHandler.SetDebugMode(true);
            touchInputHandler.SetDebugMode(false);
            
            // 如果没有异常抛出，测试通过
            Assert.IsTrue(true);
        }
        
        #endregion
        
        #region VirtualControls 测试
        
        [Test]
        public void VirtualControls_InitialState_ShouldBeZero()
        {
            // 测试虚拟控制初始状态
            Assert.AreEqual(Vector2.zero, virtualControls.GetMovementInput());
            Assert.IsFalse(virtualControls.GetJumpInput());
            Assert.IsFalse(virtualControls.GetActionInput());
            Assert.IsFalse(virtualControls.HasActiveInput());
        }
        
        [Test]
        public void VirtualControls_ResetInputs_ShouldClearAllStates()
        {
            // 测试虚拟控制重置
            virtualControls.ResetInputs();
            
            Assert.AreEqual(Vector2.zero, virtualControls.GetMovementInput());
            Assert.IsFalse(virtualControls.GetJumpInput());
            Assert.IsFalse(virtualControls.GetActionInput());
            Assert.IsFalse(virtualControls.HasActiveInput());
        }
        
        [Test]
        public void VirtualControls_SetVisible_ShouldToggleVisibility()
        {
            // 测试可见性切换
            virtualControls.SetVisible(false);
            Assert.IsFalse(virtualControls.gameObject.activeSelf);
            
            virtualControls.SetVisible(true);
            Assert.IsTrue(virtualControls.gameObject.activeSelf);
        }
        
        [Test]
        public void VirtualControls_DebugMode_ShouldToggle()
        {
            // 测试调试模式切换
            virtualControls.SetDebugMode(true);
            virtualControls.SetDebugMode(false);
            
            // 如果没有异常抛出，测试通过
            Assert.IsTrue(true);
        }
        
        #endregion
        
        #region InputMapper 测试
        
        [Test]
        public void InputMapper_InitialState_ShouldBeZero()
        {
            // 测试输入映射器初始状态
            Assert.AreEqual(Vector2.zero, inputMapper.GetMovementInput());
            Assert.IsFalse(inputMapper.GetJumpInput());
            Assert.IsFalse(inputMapper.GetActionInput());
            Assert.IsFalse(inputMapper.HasActiveInput());
        }
        
        [Test]
        public void InputMapper_EnableDisableInput_ShouldWork()
        {
            // 测试输入映射器启用/禁用
            inputMapper.EnableInput(false);
            Assert.AreEqual(Vector2.zero, inputMapper.GetMovementInput());
            Assert.IsFalse(inputMapper.HasActiveInput());
            
            inputMapper.EnableInput(true);
            // 输入应该可以正常工作
        }
        
        [Test]
        public void InputMapper_ResetInputs_ShouldClearAllStates()
        {
            // 测试输入映射器重置
            inputMapper.ResetInputs();
            
            Assert.AreEqual(Vector2.zero, inputMapper.GetMovementInput());
            Assert.IsFalse(inputMapper.GetJumpInput());
            Assert.IsFalse(inputMapper.GetActionInput());
            Assert.IsFalse(inputMapper.HasActiveInput());
        }
        
        [Test]
        public void InputMapper_GetActiveInputSource_ShouldReturnCorrectSource()
        {
            // 测试活动输入源检测
            string activeSource = inputMapper.GetActiveInputSource();
            Assert.IsNotNull(activeSource);
            
            // 初始状态应该是"None"
            Assert.AreEqual("None", activeSource);
        }
        
        [Test]
        public void InputMapper_SetVirtualControlsPriority_ShouldWork()
        {
            // 测试虚拟控制优先级设置
            inputMapper.SetVirtualControlsPriority(true);
            inputMapper.SetVirtualControlsPriority(false);
            
            // 如果没有异常抛出，测试通过
            Assert.IsTrue(true);
        }
        
        [Test]
        public void InputMapper_SetKeyboardInputEnabled_ShouldWork()
        {
            // 测试键盘输入启用设置
            inputMapper.SetKeyboardInputEnabled(true);
            inputMapper.SetKeyboardInputEnabled(false);
            
            // 如果没有异常抛出，测试通过
            Assert.IsTrue(true);
        }
        
        [Test]
        public void InputMapper_DebugMode_ShouldToggle()
        {
            // 测试调试模式切换
            inputMapper.SetDebugMode(true);
            inputMapper.SetDebugMode(false);
            
            // 如果没有异常抛出，测试通过
            Assert.IsTrue(true);
        }
        
        #endregion
        
        #region 集成测试
        
        [UnityTest]
        public IEnumerator InputSystem_Integration_ShouldWorkTogether()
        {
            // 等待一帧让组件初始化
            yield return null;
            
            // 测试所有组件是否正确初始化
            Assert.IsNotNull(touchInputHandler);
            Assert.IsNotNull(virtualControls);
            Assert.IsNotNull(inputMapper);
            
            // 测试输入映射器是否能找到其他组件
            Assert.AreEqual("None", inputMapper.GetActiveInputSource());
            
            // 测试重置功能
            inputMapper.ResetInputs();
            Assert.IsFalse(inputMapper.HasActiveInput());
        }
        
        [Test]
        public void InputSystem_IInputHandler_Interface_ShouldBeImplemented()
        {
            // 测试接口实现
            IInputHandler touchHandler = touchInputHandler;
            IInputHandler mapperHandler = inputMapper;
            
            Assert.IsNotNull(touchHandler);
            Assert.IsNotNull(mapperHandler);
            
            // 测试接口方法
            Assert.AreEqual(Vector2.zero, touchHandler.GetMovementInput());
            Assert.AreEqual(Vector2.zero, mapperHandler.GetMovementInput());
            
            Assert.IsFalse(touchHandler.GetJumpInput());
            Assert.IsFalse(mapperHandler.GetJumpInput());
            
            Assert.IsFalse(touchHandler.GetActionInput());
            Assert.IsFalse(mapperHandler.GetActionInput());
            
            Assert.IsFalse(touchHandler.HasActiveInput());
            Assert.IsFalse(mapperHandler.HasActiveInput());
        }
        
        #endregion
        
        #region 需求验证测试
        
        [Test]
        public void InputSystem_Requirement1_TouchControlsShouldWork()
        {
            // 需求1-验收标准1,2,3,4: 触控输入处理
            
            // 验证移动输入接口存在（通过调用验证）
            Assert.DoesNotThrow(() => touchInputHandler.GetMovementInput());
            
            // 验证跳跃输入接口存在（通过调用验证）
            Assert.DoesNotThrow(() => touchInputHandler.GetJumpInput());
            
            // 验证动作输入接口存在（通过调用验证）
            Assert.DoesNotThrow(() => touchInputHandler.GetActionInput());
            
            // 验证输入启用/禁用功能
            touchInputHandler.EnableInput(false);
            Assert.AreEqual(Vector2.zero, touchInputHandler.GetMovementInput());
            
            touchInputHandler.EnableInput(true);
            // 输入应该可以正常工作
        }
        
        [Test]
        public void InputSystem_Requirement1_ContinuousInputShouldWork()
        {
            // 需求1-验收标准5,6: 持续触控和释放处理
            
            // 验证输入状态跟踪
            Assert.IsFalse(touchInputHandler.HasActiveInput());
            
            // 验证输入重置功能
            touchInputHandler.ResetInputs();
            Assert.IsFalse(touchInputHandler.HasActiveInput());
            Assert.AreEqual(Vector2.zero, touchInputHandler.GetMovementInput());
        }
        
        #endregion
    }
}
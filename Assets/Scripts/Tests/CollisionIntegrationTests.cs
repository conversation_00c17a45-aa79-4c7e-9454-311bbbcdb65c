using UnityEngine;
using UnityEngine.TestTools;
using NUnit.Framework;
using System.Collections;
using MobileScrollingGame.Player;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 碰撞检测集成测试类
    /// 测试碰撞检测系统与角色控制器、移动系统的整合
    /// </summary>
    public class CollisionIntegrationTests
    {
        private GameObject testCharacter;
        private MobileScrollingGame.Player.CharacterController characterController;
        private CharacterMovement characterMovement;
        private CollisionDetector collisionDetector;
        private PlatformCollisionHandler platformHandler;
        private CharacterData characterData;
        
        // 测试环境
        private GameObject ground;
        private GameObject wall;
        private GameObject platform;
        private GameObject obstacle;
        
        [SetUp]
        public void SetUp()
        {
            // 创建测试角色
            CreateTestCharacter();
            
            // 创建测试环境
            CreateTestEnvironment();
            
            // 设置系统参数
            SetupSystems();
        }
        
        [TearDown]
        public void TearDown()
        {
            // 清理所有测试对象
            if (testCharacter != null) Object.DestroyImmediate(testCharacter);
            if (ground != null) Object.DestroyImmediate(ground);
            if (wall != null) Object.DestroyImmediate(wall);
            if (platform != null) Object.DestroyImmediate(platform);
            if (obstacle != null) Object.DestroyImmediate(obstacle);
        }
        
        /// <summary>
        /// 创建测试角色
        /// </summary>
        private void CreateTestCharacter()
        {
            testCharacter = new GameObject("TestCharacter");
            
            // 添加必要的组件
            var collider = testCharacter.AddComponent<BoxCollider2D>();
            var rigidbody = testCharacter.AddComponent<Rigidbody2D>();
            
            collisionDetector = testCharacter.AddComponent<CollisionDetector>();
            platformHandler = testCharacter.AddComponent<PlatformCollisionHandler>();
            characterMovement = testCharacter.AddComponent<CharacterMovement>();
            characterController = testCharacter.AddComponent<MobileScrollingGame.Player.CharacterController>();
            
            // 设置物理属性
            collider.size = new Vector2(1f, 2f);
            rigidbody.gravityScale = 3f;
            rigidbody.freezeRotation = true;
            rigidbody.linearDamping = 5f;
            
            // 创建角色数据
            characterData = new CharacterData
            {
                moveSpeed = 5f,
                jumpForce = 10f,
                maxHealth = 100,
                currentHealth = 100,
                isGrounded = false,
                facingRight = true
            };
            
            characterController.SetCharacterData(characterData);
        }
        
        /// <summary>
        /// 创建测试环境
        /// </summary>
        private void CreateTestEnvironment()
        {
            // 创建地面
            ground = new GameObject("Ground");
            ground.layer = 0; // Default layer
            var groundCollider = ground.AddComponent<BoxCollider2D>();
            groundCollider.size = new Vector2(20f, 2f);
            ground.transform.position = new Vector3(0f, -3f, 0f);
            
            // 创建墙壁
            wall = new GameObject("Wall");
            wall.layer = 9; // Wall layer
            var wallCollider = wall.AddComponent<BoxCollider2D>();
            wallCollider.size = new Vector2(1f, 6f);
            wall.transform.position = new Vector3(8f, 0f, 0f);
            
            // 创建平台
            platform = new GameObject("Platform");
            platform.layer = 8; // Platform layer
            var platformCollider = platform.AddComponent<BoxCollider2D>();
            platformCollider.size = new Vector2(4f, 0.5f);
            platform.transform.position = new Vector3(4f, 1f, 0f);
            
            // 创建障碍物
            obstacle = new GameObject("Obstacle");
            obstacle.layer = 10; // Obstacle layer
            var obstacleCollider = obstacle.AddComponent<BoxCollider2D>();
            obstacleCollider.size = new Vector2(1f, 1f);
            obstacle.transform.position = new Vector3(-4f, -1.5f, 0f);
        }
        
        /// <summary>
        /// 设置系统参数
        /// </summary>
        private void SetupSystems()
        {
            // 设置碰撞检测器
            LayerMask groundMask = 1; // Default layer
            LayerMask platformMask = 1 << 8; // Platform layer
            LayerMask wallMask = 1 << 9; // Wall layer
            LayerMask obstacleMask = 1 << 10; // Obstacle layer
            
            collisionDetector.SetLayerMasks(groundMask, platformMask, wallMask, obstacleMask);
            collisionDetector.SetDetectionParameters(0.2f, 0.15f, 0.3f);
            collisionDetector.SetBoundaryCheck(true, new Vector2(50f, 30f));
            
            // 设置平台处理器
            platformHandler.SetPlatformLayerMasks(platformMask, 1 << 11);
            platformHandler.SetDropThroughSettings(true, KeyCode.S, 0.5f);
            platformHandler.SetMovingPlatformSupport(true, 1f);
        }
        
        #region 基础集成测试
        
        [UnityTest]
        public IEnumerator CharacterLanding_ShouldTriggerGroundDetectionAndStopFalling()
        {
            // Arrange - 角色在空中
            testCharacter.transform.position = new Vector3(0f, 5f, 0f);
            var rigidbody = testCharacter.GetComponent<Rigidbody2D>();
            
            // Act - 等待角色落地
            yield return new WaitUntil(() => collisionDetector.IsGrounded);
            yield return new WaitForFixedUpdate(); // 等待物理更新
            
            // Assert
            Assert.IsTrue(collisionDetector.IsGrounded, "角色应该检测到地面");
            Assert.IsTrue(characterMovement.IsGrounded, "角色移动系统应该知道已着地");
            Assert.LessOrEqual(Mathf.Abs(rigidbody.linearVelocity.y), 0.5f, "角色应该停止下落");
        }
        
        [UnityTest]
        public IEnumerator CharacterJumping_ShouldLeaveGroundAndDetectAirborne()
        {
            // Arrange - 角色在地面上
            testCharacter.transform.position = new Vector3(0f, -1.5f, 0f);
            yield return new WaitUntil(() => collisionDetector.IsGrounded);
            
            // Act - 角色跳跃
            characterController.Jump();
            yield return new WaitForSeconds(0.1f); // 等待跳跃开始
            
            // Assert
            Assert.IsFalse(collisionDetector.IsGrounded, "跳跃后角色不应该在地面上");
            Assert.IsFalse(characterMovement.IsGrounded, "移动系统应该知道角色在空中");
            Assert.Greater(testCharacter.GetComponent<Rigidbody2D>().linearVelocity.y, 0f, "角色应该有向上的速度");
        }
        
        [UnityTest]
        public IEnumerator CharacterMovingToWall_ShouldDetectWallAndStopMovement()
        {
            // Arrange - 角色在地面上，远离墙壁
            testCharacter.transform.position = new Vector3(5f, -1.5f, 0f);
            yield return new WaitUntil(() => collisionDetector.IsGrounded);
            
            // Act - 向墙壁方向移动
            float moveTime = 0f;
            while (moveTime < 3f && !collisionDetector.IsAgainstWall)
            {
                characterController.Move(Vector2.right);
                moveTime += Time.deltaTime;
                yield return null;
            }
            
            // Assert
            Assert.IsTrue(collisionDetector.IsAgainstWall, "角色应该检测到墙壁");
            Assert.IsFalse(collisionDetector.CanMoveInDirection(Vector2.right), "不应该能向墙壁方向移动");
            Assert.Less(testCharacter.transform.position.x, 7.5f, "角色不应该穿过墙壁");
        }
        
        #endregion
        
        #region 平台交互集成测试
        
        [UnityTest]
        public IEnumerator CharacterJumpingToPlatform_ShouldDetectPlatformLanding()
        {
            // Arrange - 角色在地面上，平台附近
            testCharacter.transform.position = new Vector3(2f, -1.5f, 0f);
            yield return new WaitUntil(() => collisionDetector.IsGrounded);
            
            // Act - 跳跃到平台
            characterController.Jump();
            yield return new WaitForSeconds(0.2f); // 等待跳跃达到高度
            
            // 向平台方向移动
            float moveTime = 0f;
            while (moveTime < 2f && !platformHandler.IsOnPlatform)
            {
                characterController.Move(Vector2.right);
                moveTime += Time.deltaTime;
                yield return null;
            }
            
            // Assert
            Assert.IsTrue(platformHandler.IsOnPlatform, "角色应该检测到平台");
            Assert.IsTrue(collisionDetector.IsGrounded, "在平台上应该被视为着地");
            Assert.AreEqual(platform.GetComponent<Collider2D>(), platformHandler.CurrentPlatform, "当前平台应该是正确的平台");
        }
        
        [UnityTest]
        public IEnumerator CharacterFallingOffPlatform_ShouldDetectLeavingPlatform()
        {
            // Arrange - 角色在平台上
            testCharacter.transform.position = new Vector3(4f, 2f, 0f);
            yield return new WaitUntil(() => platformHandler.IsOnPlatform);
            
            // Act - 从平台边缘走下
            float moveTime = 0f;
            while (moveTime < 2f && platformHandler.IsOnPlatform)
            {
                characterController.Move(Vector2.right);
                moveTime += Time.deltaTime;
                yield return null;
            }
            
            // Assert
            Assert.IsFalse(platformHandler.IsOnPlatform, "角色应该离开平台");
            Assert.IsFalse(collisionDetector.IsGrounded, "离开平台后应该在空中");
            Assert.IsNull(platformHandler.CurrentPlatform, "当前平台引用应该被清除");
        }
        
        #endregion
        
        #region 障碍物交互集成测试
        
        [UnityTest]
        public IEnumerator CharacterApproachingObstacle_ShouldDetectAndPreventMovement()
        {
            // Arrange - 角色在地面上，远离障碍物
            testCharacter.transform.position = new Vector3(-2f, -1.5f, 0f);
            yield return new WaitUntil(() => collisionDetector.IsGrounded);
            
            // Act - 向障碍物方向移动
            float initialX = testCharacter.transform.position.x;
            float moveTime = 0f;
            
            while (moveTime < 2f)
            {
                characterController.Move(Vector2.left);
                moveTime += Time.deltaTime;
                
                // 如果检测到障碍物，停止移动
                if (collisionDetector.IsNearObstacle)
                {
                    break;
                }
                yield return null;
            }
            
            // Assert
            Assert.IsTrue(collisionDetector.IsNearObstacle, "角色应该检测到障碍物");
            Assert.IsFalse(collisionDetector.CanMoveInDirection(Vector2.left), "不应该能向障碍物方向移动");
            Assert.Greater(testCharacter.transform.position.x, -4.5f, "角色不应该穿过障碍物");
        }
        
        #endregion
        
        #region 复杂场景集成测试
        
        [UnityTest]
        public IEnumerator ComplexMovementScenario_ShouldHandleAllCollisionTypes()
        {
            // 这个测试模拟一个复杂的移动场景：
            // 1. 角色从空中落地
            // 2. 遇到障碍物
            // 3. 跳跃到平台
            // 4. 从平台跳到墙壁附近
            
            bool groundedEventTriggered = false;
            bool obstacleEventTriggered = false;
            bool platformEventTriggered = false;
            bool wallEventTriggered = false;
            
            // 订阅事件
            collisionDetector.OnGroundedChanged += (grounded) => { if (grounded) groundedEventTriggered = true; };
            collisionDetector.OnObstacleDetected += (obstacle) => { if (obstacle) obstacleEventTriggered = true; };
            platformHandler.OnPlatformEnter += (platform) => platformEventTriggered = true;
            collisionDetector.OnWallCollisionChanged += (wall) => { if (wall) wallEventTriggered = true; };
            
            // 阶段1：角色落地
            testCharacter.transform.position = new Vector3(-6f, 5f, 0f);
            yield return new WaitUntil(() => collisionDetector.IsGrounded);
            Assert.IsTrue(groundedEventTriggered, "应该触发着地事件");
            
            // 阶段2：向障碍物移动
            while (!collisionDetector.IsNearObstacle && testCharacter.transform.position.x < -3f)
            {
                characterController.Move(Vector2.right);
                yield return null;
            }
            Assert.IsTrue(obstacleEventTriggered, "应该触发障碍物检测事件");
            
            // 阶段3：跳跃并移动到平台
            characterController.Jump();
            yield return new WaitForSeconds(0.2f);
            
            while (!platformHandler.IsOnPlatform && testCharacter.transform.position.x < 6f)
            {
                characterController.Move(Vector2.right);
                yield return null;
            }
            Assert.IsTrue(platformEventTriggered, "应该触发平台进入事件");
            
            // 阶段4：继续移动到墙壁附近
            characterController.Jump();
            yield return new WaitForSeconds(0.2f);
            
            while (!collisionDetector.IsAgainstWall && testCharacter.transform.position.x < 9f)
            {
                characterController.Move(Vector2.right);
                yield return null;
            }
            Assert.IsTrue(wallEventTriggered, "应该触发墙壁碰撞事件");
            
            // 最终验证：所有系统都应该正常工作
            Assert.IsTrue(collisionDetector.IsAgainstWall, "最终应该检测到墙壁");
            Assert.IsFalse(collisionDetector.CanMoveInDirection(Vector2.right), "不应该能向墙壁方向移动");
        }
        
        #endregion
        
        #region 性能和稳定性测试
        
        [UnityTest]
        public IEnumerator StressTest_RapidMovementChanges_ShouldMaintainStability()
        {
            // Arrange - 角色在地面上
            testCharacter.transform.position = new Vector3(0f, -1.5f, 0f);
            yield return new WaitUntil(() => collisionDetector.IsGrounded);
            
            // Act - 快速改变移动方向
            for (int i = 0; i < 100; i++)
            {
                Vector2 direction = (i % 2 == 0) ? Vector2.right : Vector2.left;
                characterController.Move(direction);
                
                if (i % 10 == 0)
                {
                    characterController.Jump();
                }
                
                yield return new WaitForFixedUpdate();
            }
            
            // Assert - 系统应该保持稳定
            Assert.IsNotNull(collisionDetector, "碰撞检测器应该仍然存在");
            Assert.IsNotNull(characterController, "角色控制器应该仍然存在");
            Assert.IsFalse(float.IsNaN(testCharacter.transform.position.x) || float.IsInfinity(testCharacter.transform.position.x), "角色X位置应该是有效的");
            Assert.IsFalse(float.IsNaN(testCharacter.transform.position.y) || float.IsInfinity(testCharacter.transform.position.y), "角色Y位置应该是有效的");
        }
        
        [UnityTest]
        public IEnumerator EdgeCase_SimultaneousCollisions_ShouldHandleCorrectly()
        {
            // 创建一个角落场景，角色同时接触地面和墙壁
            testCharacter.transform.position = new Vector3(7.2f, -1.5f, 0f);
            
            // 等待检测稳定
            yield return new WaitForSeconds(0.5f);
            
            // Assert - 应该同时检测到地面和墙壁
            Assert.IsTrue(collisionDetector.IsGrounded, "应该检测到地面");
            Assert.IsTrue(collisionDetector.IsAgainstWall, "应该检测到墙壁");
            
            // 尝试移动应该被墙壁阻挡
            Vector3 initialPosition = testCharacter.transform.position;
            characterController.Move(Vector2.right);
            yield return new WaitForFixedUpdate();
            
            Assert.AreEqual(initialPosition.x, testCharacter.transform.position.x, 0.1f, "墙壁应该阻止水平移动");
        }
        
        #endregion
    }
}
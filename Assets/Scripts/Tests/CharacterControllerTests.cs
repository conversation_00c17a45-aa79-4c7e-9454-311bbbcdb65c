using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using System.Collections;
using MobileScrollingGame.Player;
using MobileScrollingGame.Core;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 角色控制器的单元测试
    /// 测试ICharacterController接口的实现
    /// </summary>
    public class CharacterControllerTests
    {
        private GameObject testCharacter;
        private MobileScrollingGame.Player.CharacterController characterController;
        private CharacterData testData;
        
        [SetUp]
        public void SetUp()
        {
            // 创建测试角色对象
            testCharacter = new GameObject("TestCharacter");
            
            // 添加必要的组件
            testCharacter.AddComponent<Rigidbody2D>();
            testCharacter.AddComponent<BoxCollider2D>();
            characterController = testCharacter.AddComponent<MobileScrollingGame.Player.CharacterController>();
            
            // 等待组件初始化
            // Unity会自动调用Awake方法
        }
        
        [TearDown]
        public void TearDown()
        {
            if (testCharacter != null)
            {
                Object.DestroyImmediate(testCharacter);
            }
        }
        
        [Test]
        public void CharacterController_ImplementsICharacterController()
        {
            // Assert
            Assert.IsTrue(characterController is ICharacterController, 
                "CharacterController应该实现ICharacterController接口");
        }
        
        [Test]
        public void GetPosition_ReturnsCorrectPosition()
        {
            // Arrange
            Vector3 testPosition = new Vector3(5f, 3f, 0f);
            testCharacter.transform.position = testPosition;
            
            // Act
            Vector3 position = characterController.GetPosition();
            
            // Assert
            Assert.AreEqual(testPosition, position, "GetPosition应该返回正确的位置");
        }
        
        [Test]
        public void GetHealth_InitialHealth_ReturnsMaxHealth()
        {
            // Act
            int health = characterController.GetHealth();
            
            // Assert
            Assert.Greater(health, 0, "初始生命值应该大于0");
        }
        
        [Test]
        public void TakeDamage_ValidDamage_ReducesHealth()
        {
            // Arrange
            int initialHealth = characterController.GetHealth();
            int damage = 20;
            
            // Act
            characterController.TakeDamage(damage);
            int newHealth = characterController.GetHealth();
            
            // Assert
            Assert.AreEqual(initialHealth - damage, newHealth, "伤害应该减少生命值");
        }
        
        [Test]
        public void TakeDamage_ExcessiveDamage_HealthDoesNotGoBelowZero()
        {
            // Arrange
            int excessiveDamage = 1000;
            
            // Act
            characterController.TakeDamage(excessiveDamage);
            int health = characterController.GetHealth();
            
            // Assert
            Assert.AreEqual(0, health, "生命值不应该低于0");
        }
        
        [Test]
        public void TakeDamage_LethalDamage_TriggersDeathEvent()
        {
            // Arrange
            bool deathEventTriggered = false;
            characterController.OnDeath += () => deathEventTriggered = true;
            int lethalDamage = characterController.GetHealth() + 10;
            
            // Act
            characterController.TakeDamage(lethalDamage);
            
            // Assert
            Assert.IsTrue(deathEventTriggered, "致命伤害应该触发死亡事件");
        }
        
        [Test]
        public void TakeDamage_TriggersHealthChangedEvent()
        {
            // Arrange
            bool healthChangedEventTriggered = false;
            int newHealthValue = 0;
            characterController.OnHealthChanged += (health) => 
            {
                healthChangedEventTriggered = true;
                newHealthValue = health;
            };
            int damage = 25;
            int expectedHealth = characterController.GetHealth() - damage;
            
            // Act
            characterController.TakeDamage(damage);
            
            // Assert
            Assert.IsTrue(healthChangedEventTriggered, "伤害应该触发生命值改变事件");
            Assert.AreEqual(expectedHealth, newHealthValue, "事件应该传递正确的生命值");
        }
        
        [Test]
        public void SetAnimation_ValidAnimationName_TriggersAnimationEvent()
        {
            // Arrange
            bool animationEventTriggered = false;
            string receivedAnimationName = "";
            string testAnimationName = "TestAnimation";
            
            characterController.OnAnimationChanged += (animName) => 
            {
                animationEventTriggered = true;
                receivedAnimationName = animName;
            };
            
            // Act
            characterController.SetAnimation(testAnimationName);
            
            // Assert
            Assert.IsTrue(animationEventTriggered, "SetAnimation应该触发动画改变事件");
            Assert.AreEqual(testAnimationName, receivedAnimationName, "应该传递正确的动画名称");
        }
        
        [Test]
        public void Move_ValidDirection_CallsMovementSystem()
        {
            // Arrange
            Vector2 moveDirection = new Vector2(1f, 0f);
            
            // Act & Assert - 不应该抛出异常
            Assert.DoesNotThrow(() => characterController.Move(moveDirection), 
                "Move方法应该能够处理有效的方向输入");
        }
        
        [Test]
        public void Jump_CallsJumpSystem()
        {
            // Act & Assert - 不应该抛出异常
            Assert.DoesNotThrow(() => characterController.Jump(), 
                "Jump方法应该能够被调用");
        }
        
        [Test]
        public void PerformAction_CallsActionSystem()
        {
            // Act & Assert - 不应该抛出异常
            Assert.DoesNotThrow(() => characterController.PerformAction(), 
                "PerformAction方法应该能够被调用");
        }
        
        [Test]
        public void SetIdleState_CallsIdleSystem()
        {
            // Act & Assert - 不应该抛出异常
            Assert.DoesNotThrow(() => characterController.SetIdleState(), 
                "SetIdleState方法应该能够被调用");
        }
        
        [Test]
        public void IsGrounded_ReturnsBoolean()
        {
            // Act
            bool isGrounded = characterController.IsGrounded();
            
            // Assert
            Assert.IsTrue(isGrounded == true || isGrounded == false, 
                "IsGrounded应该返回布尔值");
        }
        
        [Test]
        public void SetCharacterData_ValidData_UpdatesCharacterData()
        {
            // Arrange
            CharacterData newData = new CharacterData
            {
                moveSpeed = 7f,
                jumpForce = 12f,
                maxHealth = 120,
                currentHealth = 120
            };
            
            // Act
            characterController.SetCharacterData(newData);
            CharacterData retrievedData = characterController.GetCharacterData();
            
            // Assert
            Assert.AreEqual(newData.moveSpeed, retrievedData.moveSpeed, "移动速度应该被更新");
            Assert.AreEqual(newData.jumpForce, retrievedData.jumpForce, "跳跃力度应该被更新");
            Assert.AreEqual(newData.maxHealth, retrievedData.maxHealth, "最大生命值应该被更新");
        }
        
        [Test]
        public void SetCharacterData_UpdatesCurrentHealth()
        {
            // Arrange
            CharacterData newData = new CharacterData
            {
                maxHealth = 150,
                currentHealth = 150
            };
            
            // Act
            characterController.SetCharacterData(newData);
            int currentHealth = characterController.GetHealth();
            
            // Assert
            Assert.AreEqual(150, currentHealth, "当前生命值应该被更新为最大生命值");
        }
    }
}
using UnityEngine;
using UnityEngine.TestTools;
using NUnit.Framework;
using System.Collections;
using System.Collections.Generic;
using MobileScrollingGame.Camera;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 摄像机性能测试
    /// 测试摄像机裁剪优化、动态分辨率缩放和震动系统性能
    /// </summary>
    public class CameraPerformanceTests
    {
        private GameObject cameraObject;
        private UnityEngine.Camera testCamera;
        private CameraPerformanceOptimizer performanceOptimizer;
        private CameraShake cameraShake;
        private List<GameObject> testObjects;
        
        [SetUp]
        public void SetUp()
        {
            // 创建测试摄像机
            cameraObject = new GameObject("TestCamera");
            testCamera = cameraObject.AddComponent<UnityEngine.Camera>();
            testCamera.orthographic = true;
            testCamera.orthographicSize = 5f;
            
            // 添加性能优化组件
            performanceOptimizer = cameraObject.AddComponent<CameraPerformanceOptimizer>();
            cameraShake = cameraObject.AddComponent<CameraShake>();
            
            // 创建测试对象
            CreateTestObjects();
        }
        
        [TearDown]
        public void TearDown()
        {
            // 清理测试对象
            if (testObjects != null)
            {
                foreach (GameObject obj in testObjects)
                {
                    if (obj != null)
                        Object.DestroyImmediate(obj);
                }
                testObjects.Clear();
            }
            
            if (cameraObject != null)
                Object.DestroyImmediate(cameraObject);
        }
        
        /// <summary>
        /// 创建测试对象
        /// </summary>
        private void CreateTestObjects()
        {
            testObjects = new List<GameObject>();
            
            // 创建多个测试对象用于裁剪测试
            for (int i = 0; i < 50; i++)
            {
                GameObject obj = GameObject.CreatePrimitive(PrimitiveType.Cube);
                obj.name = $"TestObject_{i}";
                
                // 随机分布对象
                float x = Random.Range(-20f, 20f);
                float y = Random.Range(-20f, 20f);
                obj.transform.position = new Vector3(x, y, 0);
                
                // 添加渲染器
                Renderer renderer = obj.GetComponent<Renderer>();
                if (renderer != null)
                {
                    renderer.material = new Material(Shader.Find("Sprites/Default"));
                }
                
                testObjects.Add(obj);
            }
        }
        
        #region 裁剪优化测试
        
        [Test]
        public void TestFrustumCullingInitialization()
        {
            // 测试视锥体裁剪初始化
            Assert.IsNotNull(performanceOptimizer);
            Assert.IsNotNull(testCamera);
            
            // 启用裁剪
            performanceOptimizer.EnableFrustumCulling(true);
            
            // 验证摄像机设置
            Assert.IsTrue(testCamera.farClipPlane > 0);
        }
        
        [UnityTest]
        public IEnumerator TestFrustumCullingPerformance()
        {
            // 启用裁剪优化
            performanceOptimizer.EnableFrustumCulling(true);
            performanceOptimizer.SetCullingDistance(10f);
            
            // 等待一帧让裁剪生效
            yield return null;
            
            // 获取性能统计
            var stats = performanceOptimizer.GetPerformanceStats();
            
            // 验证有对象被裁剪
            Assert.IsTrue(stats.totalObjectsCount > 0);
            Assert.IsTrue(stats.culledObjectsCount >= 0);
            Assert.IsTrue(stats.visibleObjectsCount >= 0);
            Assert.AreEqual(stats.totalObjectsCount, stats.culledObjectsCount + stats.visibleObjectsCount);
        }
        
        [Test]
        public void TestCullingDistanceSettings()
        {
            float testDistance = 25f;
            performanceOptimizer.SetCullingDistance(testDistance);
            
            Assert.AreEqual(testDistance, testCamera.farClipPlane);
        }
        
        [UnityTest]
        public IEnumerator TestCullingWithCameraMovement()
        {
            performanceOptimizer.EnableFrustumCulling(true);
            
            // 移动摄像机到不同位置
            Vector3[] testPositions = {
                new Vector3(0, 0, -10),
                new Vector3(10, 0, -10),
                new Vector3(-10, 0, -10),
                new Vector3(0, 10, -10)
            };
            
            foreach (Vector3 position in testPositions)
            {
                cameraObject.transform.position = position;
                yield return null;
                
                var stats = performanceOptimizer.GetPerformanceStats();
                Assert.IsTrue(stats.totalObjectsCount > 0);
            }
        }
        
        #endregion
        
        #region 动态分辨率测试
        
        [Test]
        public void TestDynamicResolutionInitialization()
        {
            // 测试动态分辨率初始化
            performanceOptimizer.EnableDynamicResolution(true);
            performanceOptimizer.SetTargetFrameRate(60f);
            
            var stats = performanceOptimizer.GetPerformanceStats();
            Assert.IsTrue(stats.currentResolutionScale > 0);
            Assert.IsTrue(stats.currentResolutionScale <= 1f);
        }
        
        [Test]
        public void TestResolutionScaleSettings()
        {
            float testScale = 0.8f;
            performanceOptimizer.SetResolutionScale(testScale);
            
            var stats = performanceOptimizer.GetPerformanceStats();
            Assert.AreEqual(testScale, stats.currentResolutionScale, 0.01f);
        }
        
        [UnityTest]
        public IEnumerator TestDynamicResolutionAdjustment()
        {
            performanceOptimizer.EnableDynamicResolution(true);
            performanceOptimizer.SetTargetFrameRate(60f);
            
            // 等待几帧让系统稳定
            for (int i = 0; i < 10; i++)
            {
                yield return null;
            }
            
            var initialStats = performanceOptimizer.GetPerformanceStats();
            Assert.IsTrue(initialStats.averageFrameRate > 0);
            
            // 强制优化
            performanceOptimizer.ForceOptimization();
            yield return null;
            
            var optimizedStats = performanceOptimizer.GetPerformanceStats();
            Assert.IsTrue(optimizedStats.currentResolutionScale > 0);
        }
        
        [Test]
        public void TestTargetFrameRateSettings()
        {
            float[] testFrameRates = { 30f, 45f, 60f, 120f };
            
            foreach (float frameRate in testFrameRates)
            {
                performanceOptimizer.SetTargetFrameRate(frameRate);
                // 验证设置被接受（最小值为30）
                Assert.IsTrue(frameRate >= 30f);
            }
        }
        
        #endregion
        
        #region 震动系统性能测试
        
        [Test]
        public void TestShakeSystemInitialization()
        {
            Assert.IsNotNull(cameraShake);
            Assert.IsFalse(cameraShake.IsShaking());
        }
        
        [UnityTest]
        public IEnumerator TestBasicShakePerformance()
        {
            Vector3 originalPosition = cameraObject.transform.localPosition;
            
            // 开始震动
            cameraShake.StartShake(1f, 0.2f);
            Assert.IsTrue(cameraShake.IsShaking());
            
            // 等待震动完成
            yield return new WaitForSeconds(0.3f);
            
            Assert.IsFalse(cameraShake.IsShaking());
            
            // 验证位置恢复
            Assert.AreEqual(originalPosition, cameraObject.transform.localPosition, "震动后位置应该恢复");
        }
        
        [UnityTest]
        public IEnumerator TestConcurrentShakesPerformance()
        {
            // 启用震动池化
            cameraShake.SetShakePooling(true, 3);
            
            // 启动多个震动
            cameraShake.StartShake(1f, 0.5f);
            cameraShake.StartShake(0.8f, 0.3f);
            cameraShake.StartShake(0.6f, 0.4f);
            cameraShake.StartShake(0.4f, 0.2f); // 这个应该进入队列
            
            var stats = cameraShake.GetShakeStats();
            Assert.IsTrue(stats.activeShakeCount <= 3);
            Assert.IsTrue(stats.queuedShakeCount >= 0);
            
            // 等待所有震动完成
            yield return new WaitForSeconds(1f);
            
            Assert.IsFalse(cameraShake.IsShaking());
        }
        
        [Test]
        public void TestShakePoolingSettings()
        {
            cameraShake.SetShakePooling(true, 5);
            
            var stats = cameraShake.GetShakeStats();
            Assert.AreEqual(5, stats.maxConcurrentShakes);
        }
        
        [UnityTest]
        public IEnumerator TestSpecialShakeEffects()
        {
            // 测试微震动
            cameraShake.MicroShake(0.1f, 0.1f);
            yield return new WaitForSeconds(0.05f);
            Assert.IsTrue(cameraShake.IsShaking());
            yield return new WaitForSeconds(0.1f);
            
            // 测试脉冲震动
            cameraShake.PulseShake(1f, 0.3f, 2);
            yield return new WaitForSeconds(0.4f);
            
            // 测试渐增震动
            cameraShake.BuildupShake(1.5f, 0.5f);
            yield return new WaitForSeconds(0.6f);
            
            Assert.IsFalse(cameraShake.IsShaking());
        }
        
        #endregion
        
        #region 集成性能测试
        
        [UnityTest]
        public IEnumerator TestIntegratedPerformanceOptimization()
        {
            // 启用所有优化功能
            performanceOptimizer.EnableFrustumCulling(true);
            performanceOptimizer.EnableDynamicResolution(true);
            performanceOptimizer.SetTargetFrameRate(60f);
            
            // 同时进行震动
            cameraShake.StartShake(0.5f, 2f);
            
            // 移动摄像机
            Vector3 startPos = cameraObject.transform.position;
            Vector3 endPos = startPos + Vector3.right * 10f;
            
            float duration = 1f;
            float elapsed = 0f;
            
            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                float t = elapsed / duration;
                cameraObject.transform.position = Vector3.Lerp(startPos, endPos, t);
                
                // 检查性能统计
                var stats = performanceOptimizer.GetPerformanceStats();
                Assert.IsTrue(stats.averageFrameRate >= 0);
                Assert.IsTrue(stats.currentResolutionScale > 0);
                
                yield return null;
            }
            
            // 等待震动完成
            yield return new WaitForSeconds(1.5f);
            
            Assert.IsFalse(cameraShake.IsShaking());
        }
        
        [UnityTest]
        public IEnumerator TestPerformanceUnderStress()
        {
            // 创建更多测试对象
            for (int i = 0; i < 100; i++)
            {
                GameObject obj = GameObject.CreatePrimitive(PrimitiveType.Sphere);
                obj.transform.position = Random.insideUnitSphere * 30f;
                testObjects.Add(obj);
            }
            
            // 启用所有优化
            performanceOptimizer.EnableFrustumCulling(true);
            performanceOptimizer.EnableDynamicResolution(true);
            performanceOptimizer.SetCullingDistance(15f);
            
            // 启动多个震动
            cameraShake.SetShakePooling(true, 5);
            for (int i = 0; i < 10; i++)
            {
                cameraShake.StartShake(Random.Range(0.2f, 1f), Random.Range(0.1f, 0.5f));
            }
            
            // 运行一段时间
            float testDuration = 2f;
            float elapsed = 0f;
            
            while (elapsed < testDuration)
            {
                elapsed += Time.deltaTime;
                
                // 随机移动摄像机
                if (Random.value < 0.1f)
                {
                    cameraObject.transform.position += Random.insideUnitSphere * 2f;
                }
                
                // 检查系统仍在正常工作
                var stats = performanceOptimizer.GetPerformanceStats();
                Assert.IsTrue(stats.totalObjectsCount > 0);
                
                yield return null;
            }
            
            // 验证系统稳定性
            Assert.IsNotNull(performanceOptimizer);
            Assert.IsNotNull(cameraShake);
        }
        
        #endregion
        
        #region 性能基准测试
        
        [UnityTest]
        public IEnumerator TestFrameRateWithOptimizations()
        {
            // 测试无优化时的性能
            performanceOptimizer.EnableFrustumCulling(false);
            performanceOptimizer.EnableDynamicResolution(false);
            
            yield return new WaitForSeconds(1f);
            var statsWithoutOptimization = performanceOptimizer.GetPerformanceStats();
            
            // 启用优化
            performanceOptimizer.EnableFrustumCulling(true);
            performanceOptimizer.EnableDynamicResolution(true);
            
            yield return new WaitForSeconds(1f);
            var statsWithOptimization = performanceOptimizer.GetPerformanceStats();
            
            // 验证优化有效（裁剪了一些对象）
            Assert.IsTrue(statsWithOptimization.culledObjectsCount >= 0);
            
            Debug.Log($"无优化帧率: {statsWithoutOptimization.averageFrameRate:F1}");
            Debug.Log($"有优化帧率: {statsWithOptimization.averageFrameRate:F1}");
            Debug.Log($"裁剪对象数: {statsWithOptimization.culledObjectsCount}/{statsWithOptimization.totalObjectsCount}");
        }
        
        [UnityTest]
        public IEnumerator TestMemoryUsageOptimization()
        {
            long initialMemory = System.GC.GetTotalMemory(false);
            
            // 创建大量震动效果
            for (int i = 0; i < 50; i++)
            {
                cameraShake.StartShake(Random.Range(0.1f, 1f), Random.Range(0.1f, 0.3f));
                if (i % 10 == 0) yield return null;
            }
            
            yield return new WaitForSeconds(1f);
            
            long peakMemory = System.GC.GetTotalMemory(false);
            
            // 等待所有震动完成
            yield return new WaitForSeconds(2f);
            
            // 强制垃圾回收
            System.GC.Collect();
            yield return null;
            
            long finalMemory = System.GC.GetTotalMemory(false);
            
            Debug.Log($"初始内存: {initialMemory / 1024}KB");
            Debug.Log($"峰值内存: {peakMemory / 1024}KB");
            Debug.Log($"最终内存: {finalMemory / 1024}KB");
            
            // 验证内存没有显著泄漏
            Assert.IsTrue(finalMemory - initialMemory < 1024 * 1024); // 小于1MB差异
        }
        
        #endregion
    }
}
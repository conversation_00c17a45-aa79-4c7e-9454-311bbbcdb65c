using UnityEngine;

namespace MobileScrollingGame.Camera
{
    /// <summary>
    /// 摄像机边界组件 - 管理摄像机移动边界
    /// 防止摄像机超出关卡范围
    /// </summary>
    public class CameraBounds : MonoBehaviour
    {
        [Header("边界设置")]
        [SerializeField] private Rect bounds = new Rect(-10, -5, 20, 10);
        [SerializeField] private bool autoSetFromCollider = true;
        [SerializeField] private float padding = 1f;
        
        [Header("调试")]
        [SerializeField] private bool showBounds = true;
        [SerializeField] private Color boundsColor = Color.yellow;
        
        // 组件引用
        private CameraFollower cameraFollower;
        private BoxCollider2D boundsCollider;
        
        // 事件
        public System.Action<Rect> OnBoundsChanged;
        
        private void Awake()
        {
            InitializeComponents();
        }
        
        private void Start()
        {
            SetupBounds();
            ApplyBoundsToCamera();
        }
        
        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponents()
        {
            cameraFollower = FindFirstObjectByType<CameraFollower>();
            boundsCollider = GetComponent<BoxCollider2D>();
        }
        
        /// <summary>
        /// 设置边界
        /// </summary>
        private void SetupBounds()
        {
            if (autoSetFromCollider && boundsCollider != null)
            {
                SetBoundsFromCollider();
            }
        }
        
        /// <summary>
        /// 从碰撞器设置边界
        /// </summary>
        private void SetBoundsFromCollider()
        {
            if (boundsCollider == null) return;
            
            Vector2 center = boundsCollider.bounds.center;
            Vector2 size = boundsCollider.bounds.size;
            
            bounds = new Rect(
                center.x - size.x * 0.5f + padding,
                center.y - size.y * 0.5f + padding,
                size.x - padding * 2f,
                size.y - padding * 2f
            );
            
            OnBoundsChanged?.Invoke(bounds);
        }
        
        /// <summary>
        /// 应用边界到摄像机
        /// </summary>
        private void ApplyBoundsToCamera()
        {
            if (cameraFollower != null)
            {
                cameraFollower.SetCameraBounds(bounds);
            }
        }
        
        #region Public Methods
        
        /// <summary>
        /// 设置边界
        /// </summary>
        public void SetBounds(Rect newBounds)
        {
            bounds = newBounds;
            ApplyBoundsToCamera();
            OnBoundsChanged?.Invoke(bounds);
        }
        
        /// <summary>
        /// 设置边界（通过中心点和大小）
        /// </summary>
        public void SetBounds(Vector2 center, Vector2 size)
        {
            bounds = new Rect(
                center.x - size.x * 0.5f,
                center.y - size.y * 0.5f,
                size.x,
                size.y
            );
            ApplyBoundsToCamera();
            OnBoundsChanged?.Invoke(bounds);
        }
        
        /// <summary>
        /// 获取当前边界
        /// </summary>
        public Rect GetBounds()
        {
            return bounds;
        }
        
        /// <summary>
        /// 扩展边界
        /// </summary>
        public void ExpandBounds(float expansion)
        {
            bounds = new Rect(
                bounds.x - expansion,
                bounds.y - expansion,
                bounds.width + expansion * 2f,
                bounds.height + expansion * 2f
            );
            ApplyBoundsToCamera();
            OnBoundsChanged?.Invoke(bounds);
        }
        
        /// <summary>
        /// 检查点是否在边界内
        /// </summary>
        public bool IsPointInBounds(Vector2 point)
        {
            return bounds.Contains(point);
        }
        
        /// <summary>
        /// 将点限制在边界内
        /// </summary>
        public Vector2 ClampPointToBounds(Vector2 point)
        {
            return new Vector2(
                Mathf.Clamp(point.x, bounds.xMin, bounds.xMax),
                Mathf.Clamp(point.y, bounds.yMin, bounds.yMax)
            );
        }
        
        #endregion
        
        #region Debug
        
        private void OnDrawGizmos()
        {
            if (showBounds)
            {
                Gizmos.color = boundsColor;
                Gizmos.DrawWireCube(bounds.center, bounds.size);
                
                // 绘制边界标签
                Vector3 labelPos = new Vector3(bounds.center.x, bounds.yMax + 0.5f, 0);
                #if UNITY_EDITOR
                UnityEditor.Handles.Label(labelPos, "Camera Bounds");
                #endif
            }
        }
        
        private void OnDrawGizmosSelected()
        {
            if (showBounds)
            {
                // 绘制更详细的边界信息
                Gizmos.color = Color.red;
                Gizmos.DrawWireCube(bounds.center, bounds.size);
                
                // 绘制边界角点
                Vector2[] corners = {
                    new Vector2(bounds.xMin, bounds.yMin),
                    new Vector2(bounds.xMax, bounds.yMin),
                    new Vector2(bounds.xMax, bounds.yMax),
                    new Vector2(bounds.xMin, bounds.yMax)
                };
                
                foreach (Vector2 corner in corners)
                {
                    Gizmos.DrawWireSphere(corner, 0.2f);
                }
            }
        }
        
        #endregion
        
        #region Editor
        
        #if UNITY_EDITOR
        private void OnValidate()
        {
            if (Application.isPlaying)
            {
                ApplyBoundsToCamera();
            }
        }
        #endif
        
        #endregion
    }
}
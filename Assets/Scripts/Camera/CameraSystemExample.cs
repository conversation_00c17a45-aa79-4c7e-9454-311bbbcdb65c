using UnityEngine;
using MobileScrollingGame.Camera;

namespace MobileScrollingGame.Camera
{
    /// <summary>
    /// 摄像机系统示例 - 演示如何设置和使用摄像机跟随系统
    /// 这个脚本可以附加到场景中的GameObject来快速设置摄像机系统
    /// </summary>
    public class CameraSystemExample : MonoBehaviour
    {
        [Header("摄像机设置")]
        [SerializeField] private UnityEngine.Camera mainCamera;
        [SerializeField] private Transform playerTarget;
        
        [Header("跟随设置")]
        [SerializeField] private float followSpeed = 2f;
        [SerializeField] private Vector3 cameraOffset = new Vector3(0, 1, -10);
        [SerializeField] private float smoothTime = 0.3f;
        
        [Header("边界设置")]
        [SerializeField] private bool useBounds = true;
        [SerializeField] private Rect levelBounds = new Rect(-10, -5, 20, 10);
        
        [Header("性能优化设置")]
        [SerializeField] private bool enablePerformanceOptimization = true;
        [SerializeField] private bool enableDynamicResolution = true;
        [SerializeField] private float targetFrameRate = 60f;
        
        [Header("测试功能")]
        [SerializeField] private bool enableTestControls = true;
        [SerializeField] private KeyCode shakeTestKey = KeyCode.Space;
        [SerializeField] private KeyCode snapTestKey = KeyCode.R;
        [SerializeField] private KeyCode performanceTestKey = KeyCode.P;
        
        // 组件引用
        private CameraFollower cameraFollower;
        private CameraBounds cameraBounds;
        private CameraShake cameraShake;
        private CameraPerformanceOptimizer performanceOptimizer;
        
        private void Start()
        {
            SetupCameraSystem();
        }
        
        private void Update()
        {
            if (enableTestControls)
            {
                HandleTestInput();
            }
        }
        
        /// <summary>
        /// 设置摄像机系统
        /// </summary>
        private void SetupCameraSystem()
        {
            // 如果没有指定主摄像机，使用当前摄像机或查找主摄像机
            if (mainCamera == null)
            {
                mainCamera = GetComponent<UnityEngine.Camera>();
                if (mainCamera == null)
                {
                    mainCamera = UnityEngine.Camera.main;
                }
            }
            
            if (mainCamera == null)
            {
                Debug.LogError("CameraSystemExample: 未找到摄像机!");
                return;
            }
            
            // 设置摄像机跟随组件
            SetupCameraFollower();
            
            // 设置摄像机边界
            if (useBounds)
            {
                SetupCameraBounds();
            }
            
            // 设置摄像机震动
            SetupCameraShake();
            
            // 设置性能优化
            if (enablePerformanceOptimization)
            {
                SetupPerformanceOptimization();
            }
            
            // 查找玩家目标
            if (playerTarget == null)
            {
                FindPlayerTarget();
            }
            
            // 应用设置
            ApplyCameraSettings();
        }
        
        /// <summary>
        /// 设置摄像机跟随组件
        /// </summary>
        private void SetupCameraFollower()
        {
            cameraFollower = mainCamera.GetComponent<CameraFollower>();
            if (cameraFollower == null)
            {
                cameraFollower = mainCamera.gameObject.AddComponent<CameraFollower>();
            }
        }
        
        /// <summary>
        /// 设置摄像机边界
        /// </summary>
        private void SetupCameraBounds()
        {
            // 创建边界对象
            GameObject boundsObject = GameObject.Find("CameraBounds");
            if (boundsObject == null)
            {
                boundsObject = new GameObject("CameraBounds");
            }
            
            cameraBounds = boundsObject.GetComponent<CameraBounds>();
            if (cameraBounds == null)
            {
                cameraBounds = boundsObject.AddComponent<CameraBounds>();
            }
        }
        
        /// <summary>
        /// 设置摄像机震动
        /// </summary>
        private void SetupCameraShake()
        {
            cameraShake = mainCamera.GetComponent<CameraShake>();
            if (cameraShake == null)
            {
                cameraShake = mainCamera.gameObject.AddComponent<CameraShake>();
            }
        }
        
        /// <summary>
        /// 设置性能优化
        /// </summary>
        private void SetupPerformanceOptimization()
        {
            performanceOptimizer = mainCamera.GetComponent<CameraPerformanceOptimizer>();
            if (performanceOptimizer == null)
            {
                performanceOptimizer = mainCamera.gameObject.AddComponent<CameraPerformanceOptimizer>();
            }
        }
        
        /// <summary>
        /// 查找玩家目标
        /// </summary>
        private void FindPlayerTarget()
        {
            GameObject player = GameObject.FindGameObjectWithTag("Player");
            if (player != null)
            {
                playerTarget = player.transform;
            }
            else
            {
                // 如果没有找到带Player标签的对象，查找CharacterController
                var characterController = FindFirstObjectByType<MobileScrollingGame.Player.CharacterController>();
                if (characterController != null)
                {
                    playerTarget = characterController.transform;
                }
            }
        }
        
        /// <summary>
        /// 应用摄像机设置
        /// </summary>
        private void ApplyCameraSettings()
        {
            if (cameraFollower != null)
            {
                cameraFollower.SetFollowSpeed(followSpeed);
                cameraFollower.SetOffset(cameraOffset);
                cameraFollower.SetSmoothTime(smoothTime);
                
                if (playerTarget != null)
                {
                    cameraFollower.SetFollowTarget(playerTarget);
                }
            }
            
            if (cameraBounds != null && useBounds)
            {
                cameraBounds.SetBounds(levelBounds);
            }
            
            if (performanceOptimizer != null && enablePerformanceOptimization)
            {
                performanceOptimizer.EnableDynamicResolution(enableDynamicResolution);
                performanceOptimizer.SetTargetFrameRate(targetFrameRate);
                performanceOptimizer.EnableFrustumCulling(true);
            }
        }
        
        /// <summary>
        /// 处理测试输入
        /// </summary>
        private void HandleTestInput()
        {
            // 震动测试
            if (UnityEngine.Input.GetKeyDown(shakeTestKey) && cameraShake != null)
            {
                cameraShake.StartShake(1f, 0.5f);
                Debug.Log("摄像机震动测试");
            }
            
            // 快速移动到目标测试
            if (UnityEngine.Input.GetKeyDown(snapTestKey) && cameraFollower != null)
            {
                cameraFollower.SnapToTarget();
                Debug.Log("摄像机快速移动到目标");
            }
            
            // 性能测试
            if (UnityEngine.Input.GetKeyDown(performanceTestKey) && performanceOptimizer != null)
            {
                var stats = performanceOptimizer.GetPerformanceStats();
                Debug.Log($"性能统计 - 帧率: {stats.averageFrameRate:F1}, 分辨率缩放: {stats.currentResolutionScale:F2}, 裁剪对象: {stats.culledObjectsCount}/{stats.totalObjectsCount}");
                
                var shakeStats = cameraShake.GetShakeStats();
                Debug.Log($"震动统计 - 活动震动: {shakeStats.activeShakeCount}, 队列震动: {shakeStats.queuedShakeCount}");
            }
        }
        
        #region Public Methods
        
        /// <summary>
        /// 设置新的跟随目标
        /// </summary>
        public void SetFollowTarget(Transform target)
        {
            playerTarget = target;
            if (cameraFollower != null)
            {
                cameraFollower.SetFollowTarget(target);
            }
        }
        
        /// <summary>
        /// 设置摄像机边界
        /// </summary>
        public void SetCameraBounds(Rect bounds)
        {
            levelBounds = bounds;
            if (cameraBounds != null)
            {
                cameraBounds.SetBounds(bounds);
            }
        }
        
        /// <summary>
        /// 触发摄像机震动
        /// </summary>
        public void TriggerShake(float intensity = 1f, float duration = 0.5f)
        {
            if (cameraShake != null)
            {
                cameraShake.StartShake(intensity, duration);
            }
        }
        
        /// <summary>
        /// 启用或禁用摄像机跟随
        /// </summary>
        public void EnableFollowing(bool enabled)
        {
            if (cameraFollower != null)
            {
                cameraFollower.EnableFollowing(enabled);
            }
        }
        
        /// <summary>
        /// 启用或禁用性能优化
        /// </summary>
        public void EnablePerformanceOptimization(bool enabled)
        {
            enablePerformanceOptimization = enabled;
            if (performanceOptimizer != null)
            {
                performanceOptimizer.EnableFrustumCulling(enabled);
                performanceOptimizer.EnableDynamicResolution(enabled && enableDynamicResolution);
            }
        }
        
        /// <summary>
        /// 获取性能统计
        /// </summary>
        public CameraPerformanceOptimizer.PerformanceStats GetPerformanceStats()
        {
            if (performanceOptimizer != null)
            {
                return performanceOptimizer.GetPerformanceStats();
            }
            return new CameraPerformanceOptimizer.PerformanceStats();
        }
        
        /// <summary>
        /// 强制性能优化
        /// </summary>
        public void ForceOptimization()
        {
            if (performanceOptimizer != null)
            {
                performanceOptimizer.ForceOptimization();
            }
        }
        
        #endregion
        
        #region Debug
        
        private void OnDrawGizmosSelected()
        {
            if (useBounds)
            {
                // 绘制关卡边界
                Gizmos.color = Color.cyan;
                Gizmos.DrawWireCube(levelBounds.center, levelBounds.size);
                
                // 绘制标签
                Vector3 labelPos = new Vector3(levelBounds.center.x, levelBounds.yMax + 1f, 0);
                #if UNITY_EDITOR
                UnityEditor.Handles.Label(labelPos, "Level Bounds");
                #endif
            }
            
            // 绘制摄像机偏移
            if (playerTarget != null)
            {
                Gizmos.color = Color.green;
                Vector3 targetPos = playerTarget.position + cameraOffset;
                Gizmos.DrawWireSphere(targetPos, 0.5f);
                Gizmos.DrawLine(playerTarget.position, targetPos);
            }
        }
        
        #endregion
    }
}
using UnityEngine;
using UnityEngine.InputSystem;
using MobileScrollingGame.Core;
using System.Collections.Generic;

namespace MobileScrollingGame.Input
{
    /// <summary>
    /// 触摸数据结构
    /// </summary>
    [System.Serializable]
    public struct TouchData
    {
        public int touchId;
        public Vector2 startPosition;
        public Vector2 currentPosition;
        public float startTime;
        public float lastUpdateTime;
        public bool isValid;
        
        public TouchData(int id, Vector2 position, float time)
        {
            touchId = id;
            startPosition = position;
            currentPosition = position;
            startTime = time;
            lastUpdateTime = time;
            isValid = true;
        }
        
        public float Duration => Time.time - startTime;
        public Vector2 Delta => currentPosition - startPosition;
        public float Distance => Delta.magnitude;
    }
    
    /// <summary>
    /// 输入缓冲条目
    /// </summary>
    [System.Serializable]
    public struct InputBufferEntry
    {
        public enum InputType { Movement, Jump, Action }
        
        public InputType type;
        public Vector2 movementValue;
        public bool buttonValue;
        public float timestamp;
        
        public InputBufferEntry(InputType inputType, Vector2 movement, float time)
        {
            type = inputType;
            movementValue = movement;
            buttonValue = false;
            timestamp = time;
        }
        
        public InputBufferEntry(InputType inputType, bool button, float time)
        {
            type = inputType;
            movementValue = Vector2.zero;
            buttonValue = button;
            timestamp = time;
        }
        
        public bool IsExpired(float currentTime, float bufferTime)
        {
            return currentTime - timestamp > bufferTime;
        }
    }

    /// <summary>
    /// 处理移动设备触控输入的类
    /// 实现IInputHandler接口，将触摸事件转换为游戏动作
    /// 包含输入验证和错误处理功能
    /// </summary>
    public class TouchInputHandler : MonoBehaviour, IInputHandler
    {
        [Header("触控设置")]
        [SerializeField] private float movementDeadZone = 0.1f;
        [SerializeField] private bool debugMode = false;
        
        [Header("输入验证设置")]
        [SerializeField] private float maxTouchDistance = 1000f; // 最大有效触摸距离
        [SerializeField] private float minTouchDuration = 0.01f; // 最小有效触摸持续时间
        [SerializeField] private int maxSimultaneousTouches = 5; // 最大同时触摸数
        
        [Header("输入缓冲设置")]
        [SerializeField] private float inputBufferTime = 0.1f; // 输入缓冲时间
        [SerializeField] private int maxBufferSize = 10; // 最大缓冲大小
        
        // 输入动作引用
        private InputSystem_Actions inputActions;
        
        // 当前输入状态
        private Vector2 currentMovementInput;
        private bool jumpPressed;
        private bool actionPressed;
        private bool inputEnabled = true;
        
        // 触控状态跟踪
        private bool isMovementTouchActive;
        private int movementTouchId = -1;
        
        // 输入验证和错误处理
        private Dictionary<int, TouchData> activeTouches = new Dictionary<int, TouchData>();
        private Queue<InputBufferEntry> inputBuffer = new Queue<InputBufferEntry>();
        private float lastValidInputTime;
        private int invalidInputCount;
        private const int MAX_INVALID_INPUTS = 10;
        
        // 多点触控冲突解决
        private int primaryTouchId = -1;
        private Vector2 primaryTouchStartPosition;
        private float primaryTouchStartTime;
        
        private void Awake()
        {
            // 初始化输入动作
            inputActions = new InputSystem_Actions();
        }
        
        private void OnEnable()
        {
            // 启用输入动作
            inputActions.Enable();
            
            // 订阅输入事件
            inputActions.Player.Move.performed += OnMovePerformed;
            inputActions.Player.Move.canceled += OnMoveCanceled;
            inputActions.Player.Jump.performed += OnJumpPerformed;
            inputActions.Player.Jump.canceled += OnJumpCanceled;
            inputActions.Player.Attack.performed += OnActionPerformed;
            inputActions.Player.Attack.canceled += OnActionCanceled;
        }
        
        private void OnDisable()
        {
            // 取消订阅输入事件
            if (inputActions != null)
            {
                inputActions.Player.Move.performed -= OnMovePerformed;
                inputActions.Player.Move.canceled -= OnMoveCanceled;
                inputActions.Player.Jump.performed -= OnJumpPerformed;
                inputActions.Player.Jump.canceled -= OnJumpCanceled;
                inputActions.Player.Attack.performed -= OnActionPerformed;
                inputActions.Player.Attack.canceled -= OnActionCanceled;
                
                inputActions.Disable();
            }
        }
        
        private void OnDestroy()
        {
            inputActions?.Dispose();
        }
        
        private void Update()
        {
            // 更新输入缓冲
            UpdateInputBuffer();
            
            // 清理过期的触摸数据
            CleanupExpiredTouches();
            
            // 处理缓冲的输入
            ProcessBufferedInputs();
        }
        
        #region 输入验证和错误处理
        
        /// <summary>
        /// 验证触摸输入是否有效
        /// </summary>
        private bool ValidateTouchInput(Vector2 position, int touchId)
        {
            // 检查触摸位置是否在有效范围内
            if (position.magnitude > maxTouchDistance)
            {
                LogInvalidInput($"触摸位置超出有效范围: {position}");
                return false;
            }
            
            // 检查是否超过最大同时触摸数
            if (activeTouches.Count >= maxSimultaneousTouches && !activeTouches.ContainsKey(touchId))
            {
                LogInvalidInput($"超过最大同时触摸数: {activeTouches.Count}");
                return false;
            }
            
            // 检查触摸ID是否有效
            if (touchId < 0)
            {
                LogInvalidInput($"无效的触摸ID: {touchId}");
                return false;
            }
            
            return true;
        }
        
        /// <summary>
        /// 验证触摸持续时间
        /// </summary>
        private bool ValidateTouchDuration(int touchId)
        {
            if (activeTouches.TryGetValue(touchId, out TouchData touchData))
            {
                if (touchData.Duration < minTouchDuration)
                {
                    LogInvalidInput($"触摸持续时间过短: {touchData.Duration}");
                    return false;
                }
            }
            
            return true;
        }
        
        /// <summary>
        /// 解决多点触控冲突
        /// </summary>
        private bool ResolveTouchConflict(int touchId, Vector2 position)
        {
            // 如果没有主要触摸点，设置当前触摸为主要触摸点
            if (primaryTouchId == -1)
            {
                primaryTouchId = touchId;
                primaryTouchStartPosition = position;
                primaryTouchStartTime = Time.time;
                return true;
            }
            
            // 如果是主要触摸点，允许处理
            if (primaryTouchId == touchId)
            {
                return true;
            }
            
            // 检查是否应该切换主要触摸点
            if (activeTouches.TryGetValue(primaryTouchId, out TouchData primaryTouch))
            {
                // 如果主要触摸点已经无效或过期，切换到新的触摸点
                if (!primaryTouch.isValid || Time.time - primaryTouch.lastUpdateTime > 0.5f)
                {
                    primaryTouchId = touchId;
                    primaryTouchStartPosition = position;
                    primaryTouchStartTime = Time.time;
                    return true;
                }
            }
            
            // 拒绝非主要触摸点的移动输入
            if (debugMode)
            {
                Debug.Log($"拒绝非主要触摸点的输入: {touchId} (主要触摸点: {primaryTouchId})");
            }
            
            return false;
        }
        
        /// <summary>
        /// 添加输入到缓冲区
        /// </summary>
        private void AddToInputBuffer(InputBufferEntry.InputType type, Vector2 movement = default, bool button = false)
        {
            // 检查缓冲区大小
            if (inputBuffer.Count >= maxBufferSize)
            {
                inputBuffer.Dequeue(); // 移除最旧的输入
            }
            
            // 添加新输入
            InputBufferEntry entry;
            if (type == InputBufferEntry.InputType.Movement)
            {
                entry = new InputBufferEntry(type, movement, Time.time);
            }
            else
            {
                entry = new InputBufferEntry(type, button, Time.time);
            }
            
            inputBuffer.Enqueue(entry);
            
            if (debugMode)
            {
                Debug.Log($"添加输入到缓冲区: {type}, 缓冲区大小: {inputBuffer.Count}");
            }
        }
        
        /// <summary>
        /// 更新输入缓冲区
        /// </summary>
        private void UpdateInputBuffer()
        {
            // 移除过期的输入
            while (inputBuffer.Count > 0)
            {
                InputBufferEntry entry = inputBuffer.Peek();
                if (entry.IsExpired(Time.time, inputBufferTime))
                {
                    inputBuffer.Dequeue();
                }
                else
                {
                    break;
                }
            }
        }
        
        /// <summary>
        /// 处理缓冲的输入
        /// </summary>
        private void ProcessBufferedInputs()
        {
            if (!inputEnabled) return;
            
            // 处理缓冲区中的输入
            while (inputBuffer.Count > 0)
            {
                InputBufferEntry entry = inputBuffer.Dequeue();
                
                switch (entry.type)
                {
                    case InputBufferEntry.InputType.Movement:
                        if (entry.movementValue != Vector2.zero)
                        {
                            currentMovementInput = entry.movementValue;
                            lastValidInputTime = Time.time;
                        }
                        break;
                        
                    case InputBufferEntry.InputType.Jump:
                        if (entry.buttonValue)
                        {
                            jumpPressed = true;
                            lastValidInputTime = Time.time;
                        }
                        break;
                        
                    case InputBufferEntry.InputType.Action:
                        if (entry.buttonValue)
                        {
                            actionPressed = true;
                            lastValidInputTime = Time.time;
                        }
                        break;
                }
            }
        }
        
        /// <summary>
        /// 清理过期的触摸数据
        /// </summary>
        private void CleanupExpiredTouches()
        {
            List<int> expiredTouches = new List<int>();
            
            foreach (var kvp in activeTouches)
            {
                TouchData touchData = kvp.Value;
                if (Time.time - touchData.lastUpdateTime > 1f) // 1秒超时
                {
                    expiredTouches.Add(kvp.Key);
                }
            }
            
            foreach (int touchId in expiredTouches)
            {
                activeTouches.Remove(touchId);
                
                // 如果移除的是主要触摸点，重置主要触摸点
                if (primaryTouchId == touchId)
                {
                    primaryTouchId = -1;
                    currentMovementInput = Vector2.zero;
                    isMovementTouchActive = false;
                }
                
                if (debugMode)
                {
                    Debug.Log($"清理过期触摸: {touchId}");
                }
            }
        }
        
        /// <summary>
        /// 记录无效输入
        /// </summary>
        private void LogInvalidInput(string message)
        {
            invalidInputCount++;
            
            if (debugMode)
            {
                Debug.LogWarning($"无效输入 #{invalidInputCount}: {message}");
            }
            
            // 如果无效输入过多，可能需要重置输入系统
            if (invalidInputCount > MAX_INVALID_INPUTS)
            {
                Debug.LogError("检测到过多无效输入，重置输入系统");
                ResetInputs();
                invalidInputCount = 0;
            }
        }
        
        #endregion
        
        #region 输入事件处理
        
        private void OnMovePerformed(InputAction.CallbackContext context)
        {
            if (!inputEnabled) return;
            
            Vector2 input = context.ReadValue<Vector2>();
            
            // 验证输入
            if (!ValidateTouchInput(input, 0)) // 使用0作为默认触摸ID
            {
                return;
            }
            
            // 解决多点触控冲突
            if (!ResolveTouchConflict(0, input))
            {
                return;
            }
            
            // 应用死区
            if (input.magnitude < movementDeadZone)
            {
                input = Vector2.zero;
            }
            else
            {
                // 标准化输入并保持原始方向
                input = input.normalized * Mathf.InverseLerp(movementDeadZone, 1f, input.magnitude);
            }
            
            // 更新触摸数据
            TouchData touchData = new TouchData(0, input, Time.time);
            activeTouches[0] = touchData;
            
            // 添加到输入缓冲区
            AddToInputBuffer(InputBufferEntry.InputType.Movement, input);
            
            isMovementTouchActive = input != Vector2.zero;
            
            if (debugMode)
            {
                Debug.Log($"移动输入: {input}");
            }
        }
        
        private void OnMoveCanceled(InputAction.CallbackContext context)
        {
            // 验证触摸持续时间
            if (!ValidateTouchDuration(0))
            {
                return;
            }
            
            // 清理触摸数据
            activeTouches.Remove(0);
            
            // 重置主要触摸点
            if (primaryTouchId == 0)
            {
                primaryTouchId = -1;
            }
            
            // 添加到输入缓冲区
            AddToInputBuffer(InputBufferEntry.InputType.Movement, Vector2.zero);
            
            isMovementTouchActive = false;
            movementTouchId = -1;
            
            if (debugMode)
            {
                Debug.Log("移动输入取消");
            }
        }
        
        private void OnJumpPerformed(InputAction.CallbackContext context)
        {
            if (!inputEnabled) return;
            
            // 添加到输入缓冲区
            AddToInputBuffer(InputBufferEntry.InputType.Jump, button: true);
            
            if (debugMode)
            {
                Debug.Log("跳跃输入按下");
            }
        }
        
        private void OnJumpCanceled(InputAction.CallbackContext context)
        {
            // 跳跃输入取消时不需要特殊处理，因为跳跃是瞬时动作
            if (debugMode)
            {
                Debug.Log("跳跃输入释放");
            }
        }
        
        private void OnActionPerformed(InputAction.CallbackContext context)
        {
            if (!inputEnabled) return;
            
            // 添加到输入缓冲区
            AddToInputBuffer(InputBufferEntry.InputType.Action, button: true);
            
            if (debugMode)
            {
                Debug.Log("动作输入按下");
            }
        }
        
        private void OnActionCanceled(InputAction.CallbackContext context)
        {
            // 动作输入取消时不需要特殊处理，因为动作是瞬时动作
            if (debugMode)
            {
                Debug.Log("动作输入释放");
            }
        }
        
        #endregion
        
        #region IInputHandler 实现
        
        public Vector2 GetMovementInput()
        {
            return inputEnabled ? currentMovementInput : Vector2.zero;
        }
        
        public bool GetJumpInput()
        {
            if (!inputEnabled) return false;
            
            // 返回跳跃状态并重置（单次触发）
            bool result = jumpPressed;
            jumpPressed = false;
            return result;
        }
        
        public bool GetActionInput()
        {
            if (!inputEnabled) return false;
            
            // 返回动作状态并重置（单次触发）
            bool result = actionPressed;
            actionPressed = false;
            return result;
        }
        
        public void EnableInput(bool enabled)
        {
            inputEnabled = enabled;
            
            if (!enabled)
            {
                ResetInputs();
            }
            
            if (debugMode)
            {
                Debug.Log($"输入{(enabled ? "启用" : "禁用")}");
            }
        }
        
        public bool HasActiveInput()
        {
            return inputEnabled && (currentMovementInput != Vector2.zero || jumpPressed || actionPressed);
        }
        
        public void ResetInputs()
        {
            currentMovementInput = Vector2.zero;
            jumpPressed = false;
            actionPressed = false;
            isMovementTouchActive = false;
            movementTouchId = -1;
            
            // 清理验证和错误处理状态
            activeTouches.Clear();
            inputBuffer.Clear();
            primaryTouchId = -1;
            invalidInputCount = 0;
            lastValidInputTime = 0f;
            
            if (debugMode)
            {
                Debug.Log("输入状态重置");
            }
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 检查移动触控是否活动
        /// </summary>
        public bool IsMovementTouchActive()
        {
            return isMovementTouchActive;
        }
        
        /// <summary>
        /// 获取当前移动触控ID
        /// </summary>
        public int GetMovementTouchId()
        {
            return movementTouchId;
        }
        
        /// <summary>
        /// 设置调试模式
        /// </summary>
        public void SetDebugMode(bool debug)
        {
            debugMode = debug;
        }
        
        /// <summary>
        /// 获取当前活动触摸数量
        /// </summary>
        public int GetActiveTouchCount()
        {
            return activeTouches.Count;
        }
        
        /// <summary>
        /// 获取输入缓冲区大小
        /// </summary>
        public int GetInputBufferSize()
        {
            return inputBuffer.Count;
        }
        
        /// <summary>
        /// 获取无效输入计数
        /// </summary>
        public int GetInvalidInputCount()
        {
            return invalidInputCount;
        }
        
        /// <summary>
        /// 获取最后有效输入时间
        /// </summary>
        public float GetLastValidInputTime()
        {
            return lastValidInputTime;
        }
        
        /// <summary>
        /// 检查输入系统是否健康
        /// </summary>
        public bool IsInputSystemHealthy()
        {
            return invalidInputCount < MAX_INVALID_INPUTS && 
                   activeTouches.Count <= maxSimultaneousTouches &&
                   inputBuffer.Count <= maxBufferSize;
        }
        
        /// <summary>
        /// 强制清理输入系统
        /// </summary>
        public void ForceCleanup()
        {
            CleanupExpiredTouches();
            UpdateInputBuffer();
            
            if (debugMode)
            {
                Debug.Log("强制清理输入系统完成");
            }
        }
        
        #endregion
    }
}
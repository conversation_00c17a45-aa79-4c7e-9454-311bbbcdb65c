using UnityEngine;
using MobileScrollingGame.Core;
using MobileScrollingGame.Input;

namespace MobileScrollingGame.Input
{
    /// <summary>
    /// 输入系统使用示例
    /// 演示如何设置和使用触控输入系统
    /// </summary>
    public class InputSystemExample : MonoBehaviour
    {
        [Header("输入组件")]
        [SerializeField] private TouchInputHandler touchInputHandler;
        [SerializeField] private VirtualControls virtualControls;
        [SerializeField] private InputMapper inputMapper;
        
        [Header("测试设置")]
        [SerializeField] private bool enableDebugMode = true;
        [SerializeField] private bool showInputValues = true;
        
        // 输入状态
        private Vector2 currentMovement;
        private bool jumpPressed;
        private bool actionPressed;
        
        private void Start()
        {
            SetupInputSystem();
        }
        
        private void Update()
        {
            if (inputMapper != null)
            {
                // 获取输入
                currentMovement = inputMapper.GetMovementInput();
                jumpPressed = inputMapper.GetJumpInput();
                actionPressed = inputMapper.GetActionInput();
                
                // 显示输入值
                if (showInputValues && inputMapper.HasActiveInput())
                {
                    Debug.Log($"输入 - 移动: {currentMovement}, 跳跃: {jumpPressed}, 动作: {actionPressed}");
                }
                
                // 处理输入
                HandleInput();
            }
        }
        
        private void SetupInputSystem()
        {
            // 自动查找输入组件
            if (touchInputHandler == null)
            {
                touchInputHandler = FindFirstObjectByType<TouchInputHandler>();
            }
            
            if (virtualControls == null)
            {
                virtualControls = FindFirstObjectByType<VirtualControls>();
            }
            
            if (inputMapper == null)
            {
                inputMapper = FindFirstObjectByType<InputMapper>();
            }
            
            // 如果没有找到组件，创建它们
            if (touchInputHandler == null)
            {
                GameObject inputObject = new GameObject("TouchInputHandler");
                touchInputHandler = inputObject.AddComponent<TouchInputHandler>();
            }
            
            if (inputMapper == null)
            {
                GameObject mapperObject = new GameObject("InputMapper");
                inputMapper = mapperObject.AddComponent<InputMapper>();
            }
            
            // 设置调试模式
            if (enableDebugMode)
            {
                if (touchInputHandler != null)
                    touchInputHandler.SetDebugMode(true);
                
                if (virtualControls != null)
                    virtualControls.SetDebugMode(true);
                
                if (inputMapper != null)
                    inputMapper.SetDebugMode(true);
            }
            
            Debug.Log("输入系统设置完成");
        }
        
        private void HandleInput()
        {
            // 处理移动输入
            if (currentMovement != Vector2.zero)
            {
                // 这里可以调用角色控制器的移动方法
                // characterController.Move(currentMovement);
                
                if (showInputValues)
                {
                    Debug.Log($"处理移动输入: {currentMovement}");
                }
            }
            
            // 处理跳跃输入
            if (jumpPressed)
            {
                // 这里可以调用角色控制器的跳跃方法
                // characterController.Jump();
                
                Debug.Log("处理跳跃输入");
            }
            
            // 处理动作输入
            if (actionPressed)
            {
                // 这里可以调用角色控制器的动作方法
                // characterController.PerformAction();
                
                Debug.Log("处理动作输入");
            }
        }
        
        #region 公共方法
        
        /// <summary>
        /// 启用/禁用输入系统
        /// </summary>
        public void SetInputEnabled(bool enabled)
        {
            if (inputMapper != null)
            {
                inputMapper.EnableInput(enabled);
            }
        }
        
        /// <summary>
        /// 重置所有输入状态
        /// </summary>
        public void ResetAllInputs()
        {
            if (inputMapper != null)
            {
                inputMapper.ResetInputs();
            }
        }
        
        /// <summary>
        /// 获取当前活动的输入源
        /// </summary>
        public string GetActiveInputSource()
        {
            if (inputMapper != null)
            {
                return inputMapper.GetActiveInputSource();
            }
            return "None";
        }
        
        /// <summary>
        /// 设置虚拟控制优先级
        /// </summary>
        public void SetVirtualControlsPriority(bool prefer)
        {
            if (inputMapper != null)
            {
                inputMapper.SetVirtualControlsPriority(prefer);
            }
        }
        
        /// <summary>
        /// 切换调试模式
        /// </summary>
        public void ToggleDebugMode()
        {
            enableDebugMode = !enableDebugMode;
            
            if (touchInputHandler != null)
                touchInputHandler.SetDebugMode(enableDebugMode);
            
            if (virtualControls != null)
                virtualControls.SetDebugMode(enableDebugMode);
            
            if (inputMapper != null)
                inputMapper.SetDebugMode(enableDebugMode);
        }
        
        /// <summary>
        /// 切换输入值显示
        /// </summary>
        public void ToggleInputValueDisplay()
        {
            showInputValues = !showInputValues;
        }
        
        #endregion
        
        #region Unity编辑器方法
        
        [ContextMenu("测试输入系统")]
        private void TestInputSystem()
        {
            Debug.Log("=== 输入系统测试 ===");
            Debug.Log($"TouchInputHandler: {(touchInputHandler != null ? "已找到" : "未找到")}");
            Debug.Log($"VirtualControls: {(virtualControls != null ? "已找到" : "未找到")}");
            Debug.Log($"InputMapper: {(inputMapper != null ? "已找到" : "未找到")}");
            Debug.Log($"当前活动输入源: {GetActiveInputSource()}");
            Debug.Log("===================");
        }
        
        [ContextMenu("重置输入系统")]
        private void ResetInputSystem()
        {
            ResetAllInputs();
            Debug.Log("输入系统已重置");
        }
        
        #endregion
    }
}
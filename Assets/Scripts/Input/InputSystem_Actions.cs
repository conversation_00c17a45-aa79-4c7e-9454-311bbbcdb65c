using UnityEngine;
using UnityEngine.InputSystem;
using UnityEngine.InputSystem.Utilities;

namespace MobileScrollingGame.Input
{
    /// <summary>
    /// 自动生成的输入动作类
    /// 基于InputSystem_Actions.inputactions文件
    /// </summary>
    public class InputSystem_Actions : IInputActionCollection2, System.IDisposable
    {
        public InputActionAsset asset { get; }
        
        // 动作映射
        private readonly InputActionMap m_Player;
        private readonly InputActionMap m_UI;
        
        // 玩家动作
        private readonly InputAction m_Player_Move;
        private readonly InputAction m_Player_Look;
        private readonly InputAction m_Player_Jump;
        private readonly InputAction m_Player_Attack;
        private readonly InputAction m_Player_Interact;
        private readonly InputAction m_Player_Crouch;
        private readonly InputAction m_Player_Sprint;
        private readonly InputAction m_Player_Previous;
        private readonly InputAction m_Player_Next;
        
        // UI动作
        private readonly InputAction m_UI_Navigate;
        private readonly InputAction m_UI_Submit;
        private readonly InputAction m_UI_Cancel;
        private readonly InputAction m_UI_Point;
        private readonly InputAction m_UI_Click;
        private readonly InputAction m_UI_RightClick;
        private readonly InputAction m_UI_MiddleClick;
        private readonly InputAction m_UI_ScrollWheel;
        private readonly InputAction m_UI_TrackedDevicePosition;
        private readonly InputAction m_UI_TrackedDeviceOrientation;
        
        public InputSystem_Actions()
        {
            // 加载输入动作资源
            asset = Resources.Load<InputActionAsset>("InputSystem_Actions");
            if (asset == null)
            {
                // 如果资源不存在，创建一个基本的输入动作集
                asset = ScriptableObject.CreateInstance<InputActionAsset>();
                asset.name = "InputSystem_Actions";
                
                // 创建玩家动作映射
                m_Player = asset.AddActionMap("Player");
                m_Player_Move = m_Player.AddAction("Move", InputActionType.Value);
                m_Player_Look = m_Player.AddAction("Look", InputActionType.Value);
                m_Player_Jump = m_Player.AddAction("Jump", InputActionType.Button);
                m_Player_Attack = m_Player.AddAction("Attack", InputActionType.Button);
                m_Player_Interact = m_Player.AddAction("Interact", InputActionType.Button);
                m_Player_Crouch = m_Player.AddAction("Crouch", InputActionType.Button);
                m_Player_Sprint = m_Player.AddAction("Sprint", InputActionType.Button);
                m_Player_Previous = m_Player.AddAction("Previous", InputActionType.Button);
                m_Player_Next = m_Player.AddAction("Next", InputActionType.Button);
                
                // 创建UI动作映射
                m_UI = asset.AddActionMap("UI");
                m_UI_Navigate = m_UI.AddAction("Navigate", InputActionType.PassThrough);
                m_UI_Submit = m_UI.AddAction("Submit", InputActionType.Button);
                m_UI_Cancel = m_UI.AddAction("Cancel", InputActionType.Button);
                m_UI_Point = m_UI.AddAction("Point", InputActionType.PassThrough);
                m_UI_Click = m_UI.AddAction("Click", InputActionType.PassThrough);
                m_UI_RightClick = m_UI.AddAction("RightClick", InputActionType.PassThrough);
                m_UI_MiddleClick = m_UI.AddAction("MiddleClick", InputActionType.PassThrough);
                m_UI_ScrollWheel = m_UI.AddAction("ScrollWheel", InputActionType.PassThrough);
                m_UI_TrackedDevicePosition = m_UI.AddAction("TrackedDevicePosition", InputActionType.PassThrough);
                m_UI_TrackedDeviceOrientation = m_UI.AddAction("TrackedDeviceOrientation", InputActionType.PassThrough);
                
                // 添加基本绑定
                AddBasicBindings();
            }
            else
            {
                // 从资源中获取动作映射
                m_Player = asset.FindActionMap("Player", throwIfNotFound: true);
                m_Player_Move = m_Player.FindAction("Move", throwIfNotFound: true);
                m_Player_Look = m_Player.FindAction("Look", throwIfNotFound: true);
                m_Player_Jump = m_Player.FindAction("Jump", throwIfNotFound: true);
                m_Player_Attack = m_Player.FindAction("Attack", throwIfNotFound: true);
                m_Player_Interact = m_Player.FindAction("Interact", throwIfNotFound: true);
                m_Player_Crouch = m_Player.FindAction("Crouch", throwIfNotFound: true);
                m_Player_Sprint = m_Player.FindAction("Sprint", throwIfNotFound: true);
                m_Player_Previous = m_Player.FindAction("Previous", throwIfNotFound: true);
                m_Player_Next = m_Player.FindAction("Next", throwIfNotFound: true);
                
                m_UI = asset.FindActionMap("UI", throwIfNotFound: true);
                m_UI_Navigate = m_UI.FindAction("Navigate", throwIfNotFound: true);
                m_UI_Submit = m_UI.FindAction("Submit", throwIfNotFound: true);
                m_UI_Cancel = m_UI.FindAction("Cancel", throwIfNotFound: true);
                m_UI_Point = m_UI.FindAction("Point", throwIfNotFound: true);
                m_UI_Click = m_UI.FindAction("Click", throwIfNotFound: true);
                m_UI_RightClick = m_UI.FindAction("RightClick", throwIfNotFound: true);
                m_UI_MiddleClick = m_UI.FindAction("MiddleClick", throwIfNotFound: true);
                m_UI_ScrollWheel = m_UI.FindAction("ScrollWheel", throwIfNotFound: true);
                m_UI_TrackedDevicePosition = m_UI.FindAction("TrackedDevicePosition", throwIfNotFound: true);
                m_UI_TrackedDeviceOrientation = m_UI.FindAction("TrackedDeviceOrientation", throwIfNotFound: true);
            }
        }
        
        private void AddBasicBindings()
        {
            // 添加基本的移动绑定
            m_Player_Move.AddBinding("<Keyboard>/a").WithName("left");
            m_Player_Move.AddBinding("<Keyboard>/d").WithName("right");
            m_Player_Move.AddCompositeBinding("2DVector")
                .With("Up", "<Keyboard>/w")
                .With("Down", "<Keyboard>/s")
                .With("Left", "<Keyboard>/a")
                .With("Right", "<Keyboard>/d");
            
            // 添加触摸绑定
            m_Player_Move.AddBinding("<Touchscreen>/primaryTouch/delta");
            
            // 添加跳跃绑定
            m_Player_Jump.AddBinding("<Keyboard>/space");
            m_Player_Jump.AddBinding("<Touchscreen>/primaryTouch/tap");
            
            // 添加攻击绑定
            m_Player_Attack.AddBinding("<Keyboard>/return");
            m_Player_Attack.AddBinding("<Mouse>/leftButton");
            m_Player_Attack.AddBinding("<Touchscreen>/primaryTouch/tap");
        }
        
        public void Dispose()
        {
            UnityEngine.Object.DestroyImmediate(asset);
        }
        
        public bool Contains(InputAction action)
        {
            return asset.Contains(action);
        }
        
        public System.Collections.IEnumerator GetEnumerator()
        {
            return asset.GetEnumerator();
        }
        
        System.Collections.Generic.IEnumerator<InputAction> System.Collections.Generic.IEnumerable<InputAction>.GetEnumerator()
        {
            return asset.GetEnumerator();
        }
        
        public void Enable()
        {
            asset.Enable();
        }
        
        public void Disable()
        {
            asset.Disable();
        }
        
        public InputBinding? bindingMask
        {
            get => asset.bindingMask;
            set => asset.bindingMask = value;
        }
        
        public ReadOnlyArray<InputDevice>? devices
        {
            get => asset.devices;
            set => asset.devices = value;
        }
        
        public ReadOnlyArray<InputControlScheme> controlSchemes => asset.controlSchemes;
        
        public System.Collections.Generic.IEnumerable<InputBinding> bindings => asset.bindings;
        
        public InputAction FindAction(string actionNameOrId, bool throwIfNotFound = false)
        {
            return asset.FindAction(actionNameOrId, throwIfNotFound);
        }
        
        public int FindBinding(InputBinding bindingMask, out InputAction action)
        {
            return asset.FindBinding(bindingMask, out action);
        }
        
        // 玩家动作属性
        public PlayerActions Player => new PlayerActions(this);
        public UIActions UI => new UIActions(this);
        
        public struct PlayerActions
        {
            private InputSystem_Actions m_Wrapper;
            public PlayerActions(InputSystem_Actions wrapper) { m_Wrapper = wrapper; }
            
            public InputAction Move => m_Wrapper.m_Player_Move;
            public InputAction Look => m_Wrapper.m_Player_Look;
            public InputAction Jump => m_Wrapper.m_Player_Jump;
            public InputAction Attack => m_Wrapper.m_Player_Attack;
            public InputAction Interact => m_Wrapper.m_Player_Interact;
            public InputAction Crouch => m_Wrapper.m_Player_Crouch;
            public InputAction Sprint => m_Wrapper.m_Player_Sprint;
            public InputAction Previous => m_Wrapper.m_Player_Previous;
            public InputAction Next => m_Wrapper.m_Player_Next;
            
            public InputActionMap Get() { return m_Wrapper.m_Player; }
            public void Enable() { Get().Enable(); }
            public void Disable() { Get().Disable(); }
            public bool enabled => Get().enabled;
            
            public static implicit operator InputActionMap(PlayerActions set) { return set.Get(); }
        }
        
        public struct UIActions
        {
            private InputSystem_Actions m_Wrapper;
            public UIActions(InputSystem_Actions wrapper) { m_Wrapper = wrapper; }
            
            public InputAction Navigate => m_Wrapper.m_UI_Navigate;
            public InputAction Submit => m_Wrapper.m_UI_Submit;
            public InputAction Cancel => m_Wrapper.m_UI_Cancel;
            public InputAction Point => m_Wrapper.m_UI_Point;
            public InputAction Click => m_Wrapper.m_UI_Click;
            public InputAction RightClick => m_Wrapper.m_UI_RightClick;
            public InputAction MiddleClick => m_Wrapper.m_UI_MiddleClick;
            public InputAction ScrollWheel => m_Wrapper.m_UI_ScrollWheel;
            public InputAction TrackedDevicePosition => m_Wrapper.m_UI_TrackedDevicePosition;
            public InputAction TrackedDeviceOrientation => m_Wrapper.m_UI_TrackedDeviceOrientation;
            
            public InputActionMap Get() { return m_Wrapper.m_UI; }
            public void Enable() { Get().Enable(); }
            public void Disable() { Get().Disable(); }
            public bool enabled => Get().enabled;
            
            public static implicit operator InputActionMap(UIActions set) { return set.Get(); }
        }
    }
}
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using MobileScrollingGame.Core;

namespace MobileScrollingGame.Input
{
    /// <summary>
    /// 虚拟控制UI组件
    /// 提供移动区域、跳跃按钮和动作按钮的触控界面
    /// </summary>
    public class VirtualControls : MonoBehaviour
    {
        [Header("移动控制")]
        [SerializeField] private RectTransform movementArea;
        [SerializeField] private float movementAreaRadius = 100f;
        [SerializeField] private bool showMovementVisual = true;
        
        [Header("按钮控制")]
        [SerializeField] private Button jumpButton;
        [SerializeField] private Button actionButton;
        
        [Header("视觉反馈")]
        [SerializeField] private Image movementKnob;
        [SerializeField] private Image movementBackground;
        [SerializeField] private Color normalColor = Color.white;
        [SerializeField] private Color pressedColor = Color.gray;
        
        [Header("设置")]
        [SerializeField] private bool debugMode = false;
        
        // 移动控制状态
        private Vector2 movementInput;
        private bool isMovementActive;
        private Vector2 movementStartPosition;
        private int movementTouchId = -1;
        
        // 按钮状态
        private bool jumpPressed;
        private bool actionPressed;
        
        // 输入处理器引用
        private IInputHandler inputHandler;
        
        private void Awake()
        {
            InitializeComponents();
        }
        
        private void Start()
        {
            // 查找输入处理器
            inputHandler = FindFirstObjectByType<TouchInputHandler>();
            
            if (inputHandler == null)
            {
                Debug.LogWarning("未找到TouchInputHandler组件");
            }
        }
        
        private void InitializeComponents()
        {
            // 设置移动区域事件
            if (movementArea != null)
            {
                // 添加EventTrigger组件处理触摸事件
                EventTrigger movementTrigger = movementArea.GetComponent<EventTrigger>();
                if (movementTrigger == null)
                {
                    movementTrigger = movementArea.gameObject.AddComponent<EventTrigger>();
                }
                
                // 添加触摸开始事件
                EventTrigger.Entry pointerDownEntry = new EventTrigger.Entry();
                pointerDownEntry.eventID = EventTriggerType.PointerDown;
                pointerDownEntry.callback.AddListener(OnMovementPointerDown);
                movementTrigger.triggers.Add(pointerDownEntry);
                
                // 添加触摸拖拽事件
                EventTrigger.Entry dragEntry = new EventTrigger.Entry();
                dragEntry.eventID = EventTriggerType.Drag;
                dragEntry.callback.AddListener(OnMovementDrag);
                movementTrigger.triggers.Add(dragEntry);
                
                // 添加触摸结束事件
                EventTrigger.Entry pointerUpEntry = new EventTrigger.Entry();
                pointerUpEntry.eventID = EventTriggerType.PointerUp;
                pointerUpEntry.callback.AddListener(OnMovementPointerUp);
                movementTrigger.triggers.Add(pointerUpEntry);
            }
            
            // 设置按钮事件
            if (jumpButton != null)
            {
                jumpButton.onClick.AddListener(OnJumpButtonPressed);
            }
            
            if (actionButton != null)
            {
                actionButton.onClick.AddListener(OnActionButtonPressed);
            }
            
            // 初始化视觉元素
            UpdateMovementVisual();
        }
        
        #region 移动控制事件
        
        private void OnMovementPointerDown(BaseEventData eventData)
        {
            PointerEventData pointerData = eventData as PointerEventData;
            if (pointerData == null) return;
            
            // 记录触摸开始位置
            Vector2 localPoint;
            RectTransformUtility.ScreenPointToLocalPointInRectangle(
                movementArea, pointerData.position, pointerData.pressEventCamera, out localPoint);
            
            movementStartPosition = localPoint;
            isMovementActive = true;
            movementTouchId = pointerData.pointerId;
            
            // 计算初始移动输入
            CalculateMovementInput(localPoint);
            
            if (debugMode)
            {
                Debug.Log($"移动触摸开始: {localPoint}");
            }
        }
        
        private void OnMovementDrag(BaseEventData eventData)
        {
            if (!isMovementActive) return;
            
            PointerEventData pointerData = eventData as PointerEventData;
            if (pointerData == null || pointerData.pointerId != movementTouchId) return;
            
            // 计算当前触摸位置
            Vector2 localPoint;
            RectTransformUtility.ScreenPointToLocalPointInRectangle(
                movementArea, pointerData.position, pointerData.pressEventCamera, out localPoint);
            
            CalculateMovementInput(localPoint);
            
            if (debugMode)
            {
                Debug.Log($"移动拖拽: {localPoint}, 输入: {movementInput}");
            }
        }
        
        private void OnMovementPointerUp(BaseEventData eventData)
        {
            PointerEventData pointerData = eventData as PointerEventData;
            if (pointerData == null || pointerData.pointerId != movementTouchId) return;
            
            // 重置移动状态
            isMovementActive = false;
            movementInput = Vector2.zero;
            movementTouchId = -1;
            
            UpdateMovementVisual();
            
            if (debugMode)
            {
                Debug.Log("移动触摸结束");
            }
        }
        
        private void CalculateMovementInput(Vector2 localPoint)
        {
            // 计算从中心点的偏移
            Vector2 offset = localPoint - movementStartPosition;
            
            // 限制在移动区域半径内
            if (offset.magnitude > movementAreaRadius)
            {
                offset = offset.normalized * movementAreaRadius;
            }
            
            // 转换为标准化输入
            movementInput = offset / movementAreaRadius;
            
            UpdateMovementVisual();
        }
        
        #endregion
        
        #region 按钮事件
        
        private void OnJumpButtonPressed()
        {
            jumpPressed = true;
            
            if (debugMode)
            {
                Debug.Log("跳跃按钮按下");
            }
            
            // 视觉反馈
            StartCoroutine(ButtonPressedFeedback(jumpButton));
        }
        
        private void OnActionButtonPressed()
        {
            actionPressed = true;
            
            if (debugMode)
            {
                Debug.Log("动作按钮按下");
            }
            
            // 视觉反馈
            StartCoroutine(ButtonPressedFeedback(actionButton));
        }
        
        private System.Collections.IEnumerator ButtonPressedFeedback(Button button)
        {
            if (button == null) yield break;
            
            Image buttonImage = button.GetComponent<Image>();
            if (buttonImage == null) yield break;
            
            Color originalColor = buttonImage.color;
            buttonImage.color = pressedColor;
            
            yield return new WaitForSeconds(0.1f);
            
            buttonImage.color = originalColor;
        }
        
        #endregion
        
        #region 视觉更新
        
        private void UpdateMovementVisual()
        {
            if (!showMovementVisual) return;
            
            // 更新移动旋钮位置
            if (movementKnob != null)
            {
                Vector2 knobPosition = movementInput * movementAreaRadius;
                movementKnob.rectTransform.anchoredPosition = knobPosition;
                
                // 更新透明度
                Color knobColor = movementKnob.color;
                knobColor.a = isMovementActive ? 1f : 0.5f;
                movementKnob.color = knobColor;
            }
            
            // 更新背景透明度
            if (movementBackground != null)
            {
                Color bgColor = movementBackground.color;
                bgColor.a = isMovementActive ? 0.8f : 0.3f;
                movementBackground.color = bgColor;
            }
        }
        
        #endregion
        
        #region 公共接口
        
        /// <summary>
        /// 获取当前移动输入
        /// </summary>
        public Vector2 GetMovementInput()
        {
            return movementInput;
        }
        
        /// <summary>
        /// 获取跳跃输入状态并重置
        /// </summary>
        public bool GetJumpInput()
        {
            bool result = jumpPressed;
            jumpPressed = false;
            return result;
        }
        
        /// <summary>
        /// 获取动作输入状态并重置
        /// </summary>
        public bool GetActionInput()
        {
            bool result = actionPressed;
            actionPressed = false;
            return result;
        }
        
        /// <summary>
        /// 检查是否有活动输入
        /// </summary>
        public bool HasActiveInput()
        {
            return isMovementActive || jumpPressed || actionPressed;
        }
        
        /// <summary>
        /// 重置所有输入状态
        /// </summary>
        public void ResetInputs()
        {
            movementInput = Vector2.zero;
            isMovementActive = false;
            jumpPressed = false;
            actionPressed = false;
            movementTouchId = -1;
            
            UpdateMovementVisual();
        }
        
        /// <summary>
        /// 设置虚拟控制的可见性
        /// </summary>
        public void SetVisible(bool visible)
        {
            gameObject.SetActive(visible);
        }
        
        /// <summary>
        /// 设置调试模式
        /// </summary>
        public void SetDebugMode(bool debug)
        {
            debugMode = debug;
        }
        
        #endregion
    }
}
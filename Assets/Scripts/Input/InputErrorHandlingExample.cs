using UnityEngine;
using MobileScrollingGame.Core;
using MobileScrollingGame.Input;

namespace MobileScrollingGame.Input
{
    /// <summary>
    /// 输入错误处理示例
    /// 演示如何监控和处理输入系统错误
    /// </summary>
    public class InputErrorHandlingExample : MonoBehaviour
    {
        [Header("监控设置")]
        [SerializeField] private TouchInputHandler touchInputHandler;
        [SerializeField] private float monitoringInterval = 1f;
        [SerializeField] private bool enableAutoRecovery = true;
        
        [Header("错误阈值")]
        [SerializeField] private int maxInvalidInputs = 5;
        [SerializeField] private int maxBufferSize = 8;
        [SerializeField] private float inputTimeoutThreshold = 2f;
        
        [Header("调试信息")]
        [SerializeField] private bool showDebugInfo = true;
        [SerializeField] private bool logErrorDetails = true;
        
        // 监控状态
        private float lastMonitorTime;
        private int lastInvalidInputCount;
        private bool systemHealthy = true;
        
        // 错误统计
        private int totalErrorsDetected;
        private int autoRecoveryCount;
        private float lastErrorTime;
        
        private void Start()
        {
            // 查找TouchInputHandler
            if (touchInputHandler == null)
            {
                touchInputHandler = FindFirstObjectByType<TouchInputHandler>();
            }
            
            if (touchInputHandler == null)
            {
                Debug.LogError("未找到TouchInputHandler组件");
                enabled = false;
                return;
            }
            
            // 启用调试模式
            if (logErrorDetails)
            {
                touchInputHandler.SetDebugMode(true);
            }
            
            Debug.Log("输入错误处理监控已启动");
        }
        
        private void Update()
        {
            // 定期监控输入系统健康状态
            if (Time.time - lastMonitorTime >= monitoringInterval)
            {
                MonitorInputSystemHealth();
                lastMonitorTime = Time.time;
            }
            
            // 显示调试信息
            if (showDebugInfo)
            {
                DisplayDebugInfo();
            }
        }
        
        private void MonitorInputSystemHealth()
        {
            if (touchInputHandler == null) return;
            
            bool currentHealth = touchInputHandler.IsInputSystemHealthy();
            int currentInvalidInputs = touchInputHandler.GetInvalidInputCount();
            int currentBufferSize = touchInputHandler.GetInputBufferSize();
            int currentActiveTouches = touchInputHandler.GetActiveTouchCount();
            float lastValidInputTime = touchInputHandler.GetLastValidInputTime();
            
            // 检查系统健康状态变化
            if (systemHealthy != currentHealth)
            {
                systemHealthy = currentHealth;
                
                if (!systemHealthy)
                {
                    HandleSystemUnhealthy();
                }
                else
                {
                    HandleSystemRecovered();
                }
            }
            
            // 检查无效输入增加
            if (currentInvalidInputs > lastInvalidInputCount)
            {
                int newInvalidInputs = currentInvalidInputs - lastInvalidInputCount;
                HandleInvalidInputsDetected(newInvalidInputs);
                lastInvalidInputCount = currentInvalidInputs;
            }
            
            // 检查缓冲区溢出
            if (currentBufferSize > maxBufferSize)
            {
                HandleBufferOverflow(currentBufferSize);
            }
            
            // 检查输入超时
            if (lastValidInputTime > 0 && Time.time - lastValidInputTime > inputTimeoutThreshold)
            {
                HandleInputTimeout(Time.time - lastValidInputTime);
            }
            
            // 记录监控信息
            if (logErrorDetails)
            {
                LogMonitoringInfo(currentHealth, currentInvalidInputs, currentBufferSize, currentActiveTouches);
            }
        }
        
        private void HandleSystemUnhealthy()
        {
            totalErrorsDetected++;
            lastErrorTime = Time.time;
            
            Debug.LogWarning($"检测到输入系统不健康 (错误 #{totalErrorsDetected})");
            
            if (enableAutoRecovery)
            {
                AttemptAutoRecovery();
            }
        }
        
        private void HandleSystemRecovered()
        {
            Debug.Log("输入系统健康状态已恢复");
        }
        
        private void HandleInvalidInputsDetected(int count)
        {
            Debug.LogWarning($"检测到 {count} 个新的无效输入");
            
            if (touchInputHandler.GetInvalidInputCount() > maxInvalidInputs)
            {
                Debug.LogError("无效输入数量超过阈值，建议重置输入系统");
                
                if (enableAutoRecovery)
                {
                    AttemptAutoRecovery();
                }
            }
        }
        
        private void HandleBufferOverflow(int bufferSize)
        {
            Debug.LogWarning($"输入缓冲区溢出: {bufferSize}/{maxBufferSize}");
            
            if (enableAutoRecovery)
            {
                touchInputHandler.ForceCleanup();
                Debug.Log("执行强制清理以解决缓冲区溢出");
            }
        }
        
        private void HandleInputTimeout(float timeoutDuration)
        {
            Debug.LogWarning($"输入超时: {timeoutDuration:F2}秒");
            
            if (enableAutoRecovery)
            {
                touchInputHandler.ForceCleanup();
            }
        }
        
        private void AttemptAutoRecovery()
        {
            autoRecoveryCount++;
            
            Debug.Log($"尝试自动恢复 #{autoRecoveryCount}");
            
            // 执行恢复步骤
            touchInputHandler.ResetInputs();
            touchInputHandler.ForceCleanup();
            
            // 等待一帧后检查恢复结果
            StartCoroutine(CheckRecoveryResult());
        }
        
        private System.Collections.IEnumerator CheckRecoveryResult()
        {
            yield return null; // 等待一帧
            
            if (touchInputHandler.IsInputSystemHealthy())
            {
                Debug.Log("自动恢复成功");
            }
            else
            {
                Debug.LogError("自动恢复失败，需要手动干预");
            }
        }
        
        private void LogMonitoringInfo(bool healthy, int invalidInputs, int bufferSize, int activeTouches)
        {
            string status = healthy ? "健康" : "不健康";
            Debug.Log($"输入系统监控 - 状态: {status}, 无效输入: {invalidInputs}, 缓冲区: {bufferSize}, 活动触摸: {activeTouches}");
        }
        
        private void DisplayDebugInfo()
        {
            if (touchInputHandler == null) return;
            
            // 在屏幕上显示调试信息
            string debugText = $"输入系统状态:\n" +
                              $"健康: {(touchInputHandler.IsInputSystemHealthy() ? "是" : "否")}\n" +
                              $"无效输入: {touchInputHandler.GetInvalidInputCount()}\n" +
                              $"缓冲区大小: {touchInputHandler.GetInputBufferSize()}\n" +
                              $"活动触摸: {touchInputHandler.GetActiveTouchCount()}\n" +
                              $"总错误数: {totalErrorsDetected}\n" +
                              $"自动恢复次数: {autoRecoveryCount}";
            
            // 这里可以使用UI Text组件显示信息
            // 或者使用Debug.Log在开发模式下显示
        }
        
        #region 公共方法
        
        /// <summary>
        /// 手动触发系统检查
        /// </summary>
        [ContextMenu("手动检查系统健康")]
        public void ManualHealthCheck()
        {
            MonitorInputSystemHealth();
        }
        
        /// <summary>
        /// 手动触发自动恢复
        /// </summary>
        [ContextMenu("手动触发自动恢复")]
        public void ManualAutoRecovery()
        {
            AttemptAutoRecovery();
        }
        
        /// <summary>
        /// 重置错误统计
        /// </summary>
        [ContextMenu("重置错误统计")]
        public void ResetErrorStatistics()
        {
            totalErrorsDetected = 0;
            autoRecoveryCount = 0;
            lastErrorTime = 0f;
            lastInvalidInputCount = 0;
            
            Debug.Log("错误统计已重置");
        }
        
        /// <summary>
        /// 获取错误统计信息
        /// </summary>
        public string GetErrorStatistics()
        {
            return $"总错误数: {totalErrorsDetected}, 自动恢复次数: {autoRecoveryCount}, 最后错误时间: {lastErrorTime}";
        }
        
        /// <summary>
        /// 设置监控参数
        /// </summary>
        public void SetMonitoringParameters(float interval, int maxInvalid, int maxBuffer, float timeout)
        {
            monitoringInterval = interval;
            maxInvalidInputs = maxInvalid;
            maxBufferSize = maxBuffer;
            inputTimeoutThreshold = timeout;
            
            Debug.Log("监控参数已更新");
        }
        
        /// <summary>
        /// 启用/禁用自动恢复
        /// </summary>
        public void SetAutoRecoveryEnabled(bool enabled)
        {
            enableAutoRecovery = enabled;
            Debug.Log($"自动恢复{(enabled ? "启用" : "禁用")}");
        }
        
        #endregion
    }
}